<?php

namespace Theme25\Backend\Controller\Customer;

class CustomerGroup extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'customer/customer_group');
    }

    /**
     * Главна страница с клиентски групи - dispatcher метод
     */
    public function index() {
        $subController = $this->setBackendSubController('Customer/Customergroup/Index', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Клиентски групи');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/customer_group');
        }
    }

    /**
     * Добавяне на нова клиентска група - dispatcher метод
     */
    public function add() {
        $subController = $this->setBackendSubController('Customer/Customergroup/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Добавяне на клиентска група');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/customer_group_form');
        }
    }

    /**
     * Редактиране на клиентска група - dispatcher метод
     */
    public function edit() {
        $subController = $this->setBackendSubController('Customer/Customergroup/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Редактиране на клиентска група');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/customer_group_form');
        }
    }

    /**
     * Запазване на клиентска група - dispatcher метод
     */
    public function save() {
        $subController = $this->setBackendSubController('Customer/Customergroup/Save', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Изтриване на клиентска група - dispatcher метод
     */
    public function delete() {
        $subController = $this->setBackendSubController('Customer/Customergroup/Delete', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * AJAX методи за автокомплийт и други динамични операции
     */
    public function autocomplete() {
        $json = [];
        
        if ($this->requestGet('type')) {
            $type = $this->requestGet('type');
            
            // Динамично зареждане на суб-контролер
            $sub_controller = $this->setBackendSubController('Customer/Customergroup/' . ucfirst($type) . 'Autocomplete', $this);
            
            if ($sub_controller && is_callable([$sub_controller, 'autocomplete'])) {
                $json = $sub_controller->autocomplete($this->requestGet());
            }
        }
        
        $this->setJSONResponseOutput($json);
    }
}
