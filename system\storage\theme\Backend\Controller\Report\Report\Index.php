<?php

namespace Theme25\Backend\Controller\Report\Report;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'report-listing.js'
        ], 'footer');
    }

    /**
     * Изпълнява подготовката на данните за списъка с отчети
     */
    public function execute() {
        $this->setTitle('Отчети');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('report/report');
    }

    /**
     * Подготвя всички данни за страницата с отчети
     */
    public function prepareData() {
        $this->prepareReportsList()
             ->prepareSelectedReport()
             ->prepareReportContent()
             ->preparePageData();

        $this->setData([
            'back_url' => $this->getAdminLink('report/report')
        ]);
    }

    /**
     * Подготвя основните данни за страницата
     */
    private function preparePageData() {
        $this->setData([
            'heading_title' => 'Отчети',
            'text_type' => 'Тип отчет',
            'text_filter' => 'Филтър',
            'button_filter' => 'Филтър'
        ]);

        return $this;
    }

    /**
     * Подготвя списъка с налични отчети
     */
    private function prepareReportsList() {
        // Използваме директно същата логика като admin/controller/report/report.php
        $this->loadModel('setting/extension');

        $reports = [];

        // Преводи на отчетите на български
        $report_translations = [
            'customer_activity' => 'Отчет за активност на клиенти',
            'customer_order' => 'Отчет за поръчки на клиенти',
            'customer_reward' => 'Отчет за награди на клиенти',
            'customer_search' => 'Отчет за търсения на клиенти',
            'customer_transaction' => 'Отчет за транзакции на клиенти',
            'marketing' => 'Маркетингов отчет',
            'product_purchased' => 'Отчет за закупени продукти',
            'product_viewed' => 'Отчет за прегледани продукти',
            'sale_coupon' => 'Отчет за купони',
            'sale_order' => 'Отчет за продажби',
            'sale_return' => 'Отчет за връщания',
            'sale_shipping' => 'Отчет за доставки',
            'sale_tax' => 'Отчет за данъци'
        ];

        // Get a list of installed modules
        $extensions = $this->model_setting_extension->getInstalled('report');

        // Add all the modules which have multiple settings for each module
        foreach ($extensions as $code) {
            // if ($this->config->get('report_' . $code . '_status') && $this->hasPermission('access', 'extension/report/' . $code)) {
                // Използваме българския превод ако е наличен
                $title = isset($report_translations[$code]) ? $report_translations[$code] : 'Отчет ' . ucfirst($code);

                $reports[] = [
                    'text'       => $title,
                    'code'       => $code,
                    'sort_order' => $this->getConfig('report_' . $code . '_sort_order'),
                    'href'       => $this->getAdminLink('report/report', 'code=' . $code)
                ];
            // }
        }

        // Сортираме отчетите по sort_order
        $sort_order = [];
        foreach ($reports as $key => $value) {
            $sort_order[$key] = $value['sort_order'];
        }
        array_multisort($sort_order, SORT_ASC, $reports);

        $this->setData('reports', $reports);

        return $this;
    }

    /**
     * Определя кой отчет е избран
     */
    private function prepareSelectedReport() {
        $code = '';
        
        if ($this->requestGet('code')) {
            $code = $this->requestGet('code');
        } elseif (isset($this->data['reports'][0])) {
            $code = $this->data['reports'][0]['code'];
        }
        
        $this->setData('code', $code);
        
        return $this;
    }

    /**
     * Подготвя съдържанието на избрания отчет
     * Използва адаптираните версии с fallback към оригиналните OpenCart отчети
     */
    private function prepareReportContent() {
        $report_content = '';

        if ($this->data['code']) {
            // Първо опитваме адаптираната версия
            $class_name = $this->convertCodeToClassName($this->data['code']);
            $sub_controller = $this->setBackendSubController('Report/Report/' . $class_name, $this->_controller);
            if ($sub_controller && is_callable([$sub_controller, 'report'])) {
                // Използваме адаптираната версия
                try {
                    $report_content = $sub_controller->report();
                } catch (Exception $e) {
                    $report_content = '';
                }
            }

            // Ако адаптираната версия не работи, fallback към оригиналната
            if (empty($report_content)) {
                try {
                    $report_content = $this->loadController('extension/report/' . $this->data['code'] . '/report');
                } catch (Exception $e) {
                    $report_content = '<div class="alert alert-warning">Отчетът не може да бъде зареден.</div>';
                }
            }
        } elseif (isset($this->data['reports'][0])) {
            // Ако няма избран код, зареждаме първия отчет
            $first_code = $this->data['reports'][0]['code'];
            $class_name = $this->convertCodeToClassName($first_code);
            $sub_controller = $this->setBackendSubController('Report/Report/' . $class_name, $this->_controller);

            if ($sub_controller && is_callable([$sub_controller, 'report'])) {
                try {
                    $report_content = $sub_controller->report();
                } catch (Exception $e) {
                    $report_content = '';
                }
            }

            if (empty($report_content)) {
                try {
                    $report_content = $this->loadController('extension/report/' . $first_code . '/report');
                } catch (Exception $e) {
                    $report_content = '<div class="alert alert-warning">Отчетът не може да бъде зареден.</div>';
                }
            }
        }

    

        $this->setData('report', $report_content);

        return $this;
    }

    /**
     * Конвертира код на отчет към име на клас
     */
    private function convertCodeToClassName($code) {
        // Конвертираме snake_case към PascalCase
        $parts = explode('_', $code);
        $class_name = '';

        foreach ($parts as $part) {
            $class_name .= ucfirst($part);
        }

        return $class_name;
    }
}
