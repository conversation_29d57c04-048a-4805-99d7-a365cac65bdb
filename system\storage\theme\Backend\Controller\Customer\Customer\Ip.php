<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Ip extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('customers.log');
    }

    /**
     * Изпълнява управление на IP адреси - връща HTML за tab view
     */
    public function execute() {
        try {
            $customer_id = (int)$this->requestGet('customer_id', 0);

            if (!$customer_id) {
                echo '<p class="text-gray-500">Невалиден клиент!</p>';
                return;
            }

            if (!$this->hasPermission('modify', 'customer/customer')) {
                echo '<p class="text-red-500">Нямате права за управление на IP адреси!</p>';
                return;
            }

            $this->loadModelAs('customer/customer', 'customerModel');
            $customer_info = $this->customerModel->getCustomer($customer_id);

            if (!$customer_info) {
                echo '<p class="text-red-500">Клиентът не съществува!</p>';
                return;
            }

            // Получаваме IP адресите
            $ip_data = $this->getIpAddressesData($customer_id);

            // Генерираме HTML
            echo $this->generateIpListHtml($ip_data);

        } catch (Exception $e) {
            echo '<p class="text-red-500">Грешка при зареждане на IP адреси: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
    }

    /**
     * API метод за JSON операции
     */
    public function api() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за управление на IP адреси!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            $action = $this->requestGet('action', '');

            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } else {
                $this->loadModelAs('customer/customer', 'customerModel');
                $customer_info = $this->customerModel->getCustomer($customer_id);

                if (!$customer_info) {
                    $json['error'] = 'Клиентът не съществува!';
                } else {
                    switch ($action) {
                        case 'add':
                            $json = $this->addIpAddress($customer_id);
                            break;
                        case 'list':
                            $json = $this->getIpAddresses($customer_id);
                            break;
                        case 'delete':
                            $json = $this->deleteIpAddress();
                            break;
                        case 'ban':
                            $json = $this->banIpAddress($customer_id);
                            break;
                        case 'unban':
                            $json = $this->unbanIpAddress();
                            break;
                        default:
                            $json = $this->getIpAddresses($customer_id);
                            break;
                    }
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Добавя нов IP адрес
     */
    private function addIpAddress($customer_id) {
        $json = [];
        
        if ($this->getServer('REQUEST_METHOD') == 'POST') {
            $ip = trim($this->requestPost('ip', ''));
            
            if (empty($ip)) {
                $json['error'] = 'IP адресът е задължителен!';
            } elseif (!filter_var($ip, FILTER_VALIDATE_IP)) {
                $json['error'] = 'Невалиден IP адрес!';
            } else {
                // Създаваме таблицата ако не съществува
                $this->createCustomerIpTableIfNotExists();

                // Проверяваме дали IP адресът вече съществува за този клиент
                $existing = $this->dbQuery("SELECT customer_ip_id FROM `" . DB_PREFIX . "customer_ip` WHERE customer_id = '" . (int)$customer_id . "' AND ip = '" . $this->dbEscape($ip) . "'");

                if ($existing->num_rows) {
                    $json['error'] = 'Този IP адрес вече е добавен за клиента!';
                } else {
                    $this->dbQuery("INSERT INTO `" . DB_PREFIX . "customer_ip` SET
                        customer_id = '" . (int)$customer_id . "',
                        ip = '" . $this->dbEscape($ip) . "',
                        date_added = NOW()");

                    // Логваме действието
                    $this->loadModelAs('user/user', 'userModel');
                    $user_info = $this->userModel->getUser($this->user->getId());

                    if ($user_info) {
                        $this->log->write('Администратор ' . $user_info['username'] . ' добави IP адрес ' . $ip . ' за клиент ID: ' . $customer_id);
                    }

                    $json['success'] = 'IP адресът беше успешно добавен!';
                }
            }
        } else {
            $json['error'] = 'Невалидна заявка!';
        }
        
        return $json;
    }

    /**
     * Генерира HTML за списъка с IP адреси
     */
    private function generateIpListHtml($ip_data) {
        if (empty($ip_data['ip_addresses'])) {
            return '<p class="text-gray-500">Няма записани IP адреси за този клиент.</p>';
        }

        $html = '<div class="space-y-3">';

        foreach ($ip_data['ip_addresses'] as $ip) {
            $statusClass = $ip['is_banned'] ? 'bg-red-100 border-red-200' : 'bg-white border-gray-200';
            $statusText = $ip['is_banned'] ? 'Блокиран' : 'Активен';
            $statusIcon = $ip['is_banned'] ? 'ri-close-circle-line text-red-600' : 'ri-check-circle-line text-green-600';

            $html .= '
                <div class="flex items-center justify-between p-4 border rounded-lg ' . $statusClass . '">
                    <div class="flex items-center space-x-3">
                        <i class="' . $statusIcon . '"></i>
                        <div>
                            <div class="font-medium text-gray-900">' . htmlspecialchars($ip['ip']) . '</div>
                            <div class="text-sm text-gray-500">
                                Добавен: ' . htmlspecialchars($ip['date_added']) . ' |
                                Общо клиенти с този IP: ' . (int)$ip['total_customers'] . '
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ' .
                        ($ip['is_banned'] ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800') . '">' .
                        $statusText . '</span>
                    </div>
                </div>';
        }

        $html .= '</div>';

        // Добавяме информация за общия брой
        if ($ip_data['total'] > count($ip_data['ip_addresses'])) {
            $html .= '<div class="mt-4 text-sm text-gray-500 text-center">
                Показани ' . count($ip_data['ip_addresses']) . ' от ' . $ip_data['total'] . ' IP адреса
            </div>';
        }

        return $html;
    }

    /**
     * Получава данните за IP адресите (за HTML генериране)
     */
    private function getIpAddressesData($customer_id) {
        $start = (int)$this->requestGet('start', 0);
        $limit = (int)$this->requestGet('limit', 20);

        // Използваме стандартния OpenCart модел
        $this->loadModelAs('customer/customer', 'customerModel');

        $ips = $this->customerModel->getIps($customer_id, $start, $limit);
        $total = $this->customerModel->getTotalIps($customer_id);

        $ip_data = [];
        foreach ($ips as $row) {
            $ip_data[] = [
                'customer_ip_id' => isset($row['customer_ip_id']) ? $row['customer_ip_id'] : 0,
                'ip' => $row['ip'],
                'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                'is_banned' => $this->isIpBanned($row['ip']),
                'total_customers' => $this->customerModel->getTotalCustomersByIp($row['ip'])
            ];
        }

        return [
            'ip_addresses' => $ip_data,
            'total' => $total
        ];
    }

    /**
     * Връща списък с IP адреси (за JSON API)
     */
    private function getIpAddresses($customer_id) {
        $start = (int)$this->requestGet('start', 0);
        $limit = (int)$this->requestGet('limit', 20);
        
        $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "customer_ip` WHERE customer_id = '" . (int)$customer_id . "' ORDER BY date_added DESC LIMIT " . (int)$start . "," . (int)$limit);
        
        $total_query = $this->dbQuery("SELECT COUNT(*) as total FROM `" . DB_PREFIX . "customer_ip` WHERE customer_id = '" . (int)$customer_id . "'");
        
        $ip_data = [];
        foreach ($query->rows as $row) {
            $ip_data[] = [
                'customer_ip_id' => $row['customer_ip_id'],
                'ip' => $row['ip'],
                'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                'is_banned' => $this->isIpBanned($row['ip'])
            ];
        }
        
        return [
            'ip_addresses' => $ip_data,
            'total' => $total_query->row['total']
        ];
    }

    /**
     * Изтрива IP адрес
     */
    private function deleteIpAddress() {
        $json = [];
        
        $ip_id = (int)$this->requestGet('ip_id', 0);
        
        if (!$ip_id) {
            $json['error'] = 'Невалиден IP адрес!';
        } else {
            // Проверяваме дали IP адресът съществува
            $ip_info = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "customer_ip` WHERE customer_ip_id = '" . (int)$ip_id . "'");
            
            if (!$ip_info->num_rows) {
                $json['error'] = 'IP адресът не съществува!';
            } else {
                $this->dbQuery("DELETE FROM `" . DB_PREFIX . "customer_ip` WHERE customer_ip_id = '" . (int)$ip_id . "'");
                
                // Логваме действието
                $this->load->model('user/user');
                $user_info = $this->model_user_user->getUser($this->user->getId());
                
                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' изтри IP адрес ' . $ip_info->row['ip']);
                }
                
                $json['success'] = 'IP адресът беше успешно изтрит!';
            }
        }
        
        return $json;
    }

    /**
     * Блокира IP адрес
     */
    private function banIpAddress($customer_id) {
        $json = [];
        
        $ip = trim($this->requestPost('ip', ''));
        
        if (empty($ip)) {
            $json['error'] = 'IP адресът е задължителен!';
        } elseif (!filter_var($ip, FILTER_VALIDATE_IP)) {
            $json['error'] = 'Невалиден IP адрес!';
        } else {
            // Създаваме таблицата ако не съществува
            $this->createCustomerBanIpTableIfNotExists();

            // Проверяваме дали IP адресът вече е блокиран
            $existing = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "customer_ban_ip` WHERE ip = '" . $this->dbEscape($ip) . "'");

            if ($existing->num_rows) {
                $json['error'] = 'Този IP адрес вече е блокиран!';
            } else {
                $this->dbQuery("INSERT INTO `" . DB_PREFIX . "customer_ban_ip` SET
                    ip = '" . $this->dbEscape($ip) . "',
                    date_added = NOW()");

                // Логваме действието
                $this->loadModelAs('user/user', 'userModel');
                $user_info = $this->userModel->getUser($this->user->getId());

                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' блокира IP адрес ' . $ip);
                }

                $json['success'] = 'IP адресът беше успешно блокиран!';
            }
        }
        
        return $json;
    }

    /**
     * Отблокира IP адрес
     */
    private function unbanIpAddress() {
        $json = [];
        
        $ip = trim($this->requestPost('ip', ''));
        
        if (empty($ip)) {
            $json['error'] = 'IP адресът е задължителен!';
        } else {
            $result = $this->dbQuery("DELETE FROM `" . DB_PREFIX . "customer_ban_ip` WHERE ip = '" . $this->dbEscape($ip) . "'");
            
            if ($this->db->countAffected() > 0) {
                // Логваме действието
                $this->loadModelAs('user/user', 'userModel');
                $user_info = $this->userModel->getUser($this->user->getId());
                
                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' отблокира IP адрес ' . $ip);
                }
                
                $json['success'] = 'IP адресът беше успешно отблокиран!';
            } else {
                $json['error'] = 'IP адресът не е блокиран!';
            }
        }
        
        return $json;
    }

    /**
     * Проверява дали IP адрес е блокиран
     */
    private function isIpBanned($ip) {
        try {
            // Създаваме таблицата ако не съществува
            $this->createCustomerBanIpTableIfNotExists();

            $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "customer_ban_ip` WHERE ip = '" . $this->dbEscape($ip) . "'");
            return $query->num_rows > 0;
        } catch (Exception $e) {
            $this->writeLog('Грешка при проверка на блокиран IP: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Създава таблицата customer_ip ако не съществува
     */
    private function createCustomerIpTableIfNotExists() {
        try {
            $this->dbQuery("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "customer_ip` (
                `customer_ip_id` int(11) NOT NULL AUTO_INCREMENT,
                `customer_id` int(11) NOT NULL,
                `ip` varchar(40) NOT NULL,
                `date_added` datetime NOT NULL,
                PRIMARY KEY (`customer_ip_id`),
                KEY `customer_id` (`customer_id`),
                KEY `ip` (`ip`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci");
        } catch (Exception $e) {
            $this->writeLog('Грешка при създаване на таблица customer_ip: ' . $e->getMessage());
        }
    }

    /**
     * Създава таблицата customer_ban_ip ако не съществува
     */
    private function createCustomerBanIpTableIfNotExists() {
        try {
            $this->dbQuery("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "customer_ban_ip` (
                `customer_ban_ip_id` int(11) NOT NULL AUTO_INCREMENT,
                `ip` varchar(40) NOT NULL,
                `date_added` datetime NOT NULL,
                PRIMARY KEY (`customer_ban_ip_id`),
                UNIQUE KEY `ip` (`ip`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci");
        } catch (Exception $e) {
            $this->writeLog('Грешка при създаване на таблица customer_ban_ip: ' . $e->getMessage());
        }
    }
}
