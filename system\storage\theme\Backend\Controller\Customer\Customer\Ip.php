<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Ip extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява управление на IP адреси
     */
    public function execute() {
        $json = [];
        
        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за управление на IP адреси!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            $action = $this->requestGet('action', '');
            
            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } else {
                $this->load->model('customer/customer');
                $customer_info = $this->model_customer_customer->getCustomer($customer_id);
                
                if (!$customer_info) {
                    $json['error'] = 'Клиентът не съществува!';
                } else {
                    switch ($action) {
                        case 'add':
                            $json = $this->addIpAddress($customer_id);
                            break;
                        case 'list':
                            $json = $this->getIpAddresses($customer_id);
                            break;
                        case 'delete':
                            $json = $this->deleteIpAddress();
                            break;
                        case 'ban':
                            $json = $this->banIpAddress($customer_id);
                            break;
                        case 'unban':
                            $json = $this->unbanIpAddress();
                            break;
                        default:
                            $json = $this->getIpAddresses($customer_id);
                            break;
                    }
                }
            }
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Добавя нов IP адрес
     */
    private function addIpAddress($customer_id) {
        $json = [];
        
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $ip = trim($this->requestPost('ip', ''));
            
            if (empty($ip)) {
                $json['error'] = 'IP адресът е задължителен!';
            } elseif (!filter_var($ip, FILTER_VALIDATE_IP)) {
                $json['error'] = 'Невалиден IP адрес!';
            } else {
                // Създаваме таблицата ако не съществува
                $this->createCustomerIpTableIfNotExists();

                // Проверяваме дали IP адресът вече съществува за този клиент
                $existing = $this->db->query("SELECT customer_ip_id FROM `" . DB_PREFIX . "customer_ip` WHERE customer_id = '" . (int)$customer_id . "' AND ip = '" . $this->db->escape($ip) . "'");

                if ($existing->num_rows) {
                    $json['error'] = 'Този IP адрес вече е добавен за клиента!';
                } else {
                    $this->db->query("INSERT INTO `" . DB_PREFIX . "customer_ip` SET
                        customer_id = '" . (int)$customer_id . "',
                        ip = '" . $this->db->escape($ip) . "',
                        date_added = NOW()");

                    // Логваме действието
                    $this->load->model('user/user');
                    $user_info = $this->model_user_user->getUser($this->user->getId());

                    if ($user_info) {
                        $this->log->write('Администратор ' . $user_info['username'] . ' добави IP адрес ' . $ip . ' за клиент ID: ' . $customer_id);
                    }

                    $json['success'] = 'IP адресът беше успешно добавен!';
                }
            }
        } else {
            $json['error'] = 'Невалидна заявка!';
        }
        
        return $json;
    }

    /**
     * Връща списък с IP адреси
     */
    private function getIpAddresses($customer_id) {
        $start = (int)$this->requestGet('start', 0);
        $limit = (int)$this->requestGet('limit', 20);
        
        $query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "customer_ip` WHERE customer_id = '" . (int)$customer_id . "' ORDER BY date_added DESC LIMIT " . (int)$start . "," . (int)$limit);
        
        $total_query = $this->db->query("SELECT COUNT(*) as total FROM `" . DB_PREFIX . "customer_ip` WHERE customer_id = '" . (int)$customer_id . "'");
        
        $ip_data = [];
        foreach ($query->rows as $row) {
            $ip_data[] = [
                'customer_ip_id' => $row['customer_ip_id'],
                'ip' => $row['ip'],
                'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                'is_banned' => $this->isIpBanned($row['ip'])
            ];
        }
        
        return [
            'ip_addresses' => $ip_data,
            'total' => $total_query->row['total']
        ];
    }

    /**
     * Изтрива IP адрес
     */
    private function deleteIpAddress() {
        $json = [];
        
        $ip_id = (int)$this->requestGet('ip_id', 0);
        
        if (!$ip_id) {
            $json['error'] = 'Невалиден IP адрес!';
        } else {
            // Проверяваме дали IP адресът съществува
            $ip_info = $this->db->query("SELECT * FROM `" . DB_PREFIX . "customer_ip` WHERE customer_ip_id = '" . (int)$ip_id . "'");
            
            if (!$ip_info->num_rows) {
                $json['error'] = 'IP адресът не съществува!';
            } else {
                $this->db->query("DELETE FROM `" . DB_PREFIX . "customer_ip` WHERE customer_ip_id = '" . (int)$ip_id . "'");
                
                // Логваме действието
                $this->load->model('user/user');
                $user_info = $this->model_user_user->getUser($this->user->getId());
                
                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' изтри IP адрес ' . $ip_info->row['ip']);
                }
                
                $json['success'] = 'IP адресът беше успешно изтрит!';
            }
        }
        
        return $json;
    }

    /**
     * Блокира IP адрес
     */
    private function banIpAddress($customer_id) {
        $json = [];
        
        $ip = trim($this->requestPost('ip', ''));
        
        if (empty($ip)) {
            $json['error'] = 'IP адресът е задължителен!';
        } elseif (!filter_var($ip, FILTER_VALIDATE_IP)) {
            $json['error'] = 'Невалиден IP адрес!';
        } else {
            // Създаваме таблицата ако не съществува
            $this->createCustomerBanIpTableIfNotExists();

            // Проверяваме дали IP адресът вече е блокиран
            $existing = $this->db->query("SELECT * FROM `" . DB_PREFIX . "customer_ban_ip` WHERE ip = '" . $this->db->escape($ip) . "'");

            if ($existing->num_rows) {
                $json['error'] = 'Този IP адрес вече е блокиран!';
            } else {
                $this->db->query("INSERT INTO `" . DB_PREFIX . "customer_ban_ip` SET
                    ip = '" . $this->db->escape($ip) . "',
                    date_added = NOW()");

                // Логваме действието
                $this->load->model('user/user');
                $user_info = $this->model_user_user->getUser($this->user->getId());

                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' блокира IP адрес ' . $ip);
                }

                $json['success'] = 'IP адресът беше успешно блокиран!';
            }
        }
        
        return $json;
    }

    /**
     * Отблокира IP адрес
     */
    private function unbanIpAddress() {
        $json = [];
        
        $ip = trim($this->requestPost('ip', ''));
        
        if (empty($ip)) {
            $json['error'] = 'IP адресът е задължителен!';
        } else {
            $result = $this->db->query("DELETE FROM `" . DB_PREFIX . "customer_ban_ip` WHERE ip = '" . $this->db->escape($ip) . "'");
            
            if ($this->db->countAffected() > 0) {
                // Логваме действието
                $this->load->model('user/user');
                $user_info = $this->model_user_user->getUser($this->user->getId());
                
                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' отблокира IP адрес ' . $ip);
                }
                
                $json['success'] = 'IP адресът беше успешно отблокиран!';
            } else {
                $json['error'] = 'IP адресът не е блокиран!';
            }
        }
        
        return $json;
    }

    /**
     * Проверява дали IP адрес е блокиран
     */
    private function isIpBanned($ip) {
        try {
            $query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "customer_ban_ip` WHERE ip = '" . $this->db->escape($ip) . "'");
            return $query->num_rows > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Създава таблицата customer_ip ако не съществува
     */
    private function createCustomerIpTableIfNotExists() {
        try {
            $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "customer_ip` (
                `customer_ip_id` int(11) NOT NULL AUTO_INCREMENT,
                `customer_id` int(11) NOT NULL,
                `ip` varchar(40) NOT NULL,
                `date_added` datetime NOT NULL,
                PRIMARY KEY (`customer_ip_id`),
                KEY `customer_id` (`customer_id`),
                KEY `ip` (`ip`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci");
        } catch (Exception $e) {
            $this->log->write('Грешка при създаване на таблица customer_ip: ' . $e->getMessage());
        }
    }

    /**
     * Създава таблицата customer_ban_ip ако не съществува
     */
    private function createCustomerBanIpTableIfNotExists() {
        try {
            $this->db->query("CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "customer_ban_ip` (
                `customer_ban_ip_id` int(11) NOT NULL AUTO_INCREMENT,
                `ip` varchar(40) NOT NULL,
                `date_added` datetime NOT NULL,
                PRIMARY KEY (`customer_ban_ip_id`),
                UNIQUE KEY `ip` (`ip`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci");
        } catch (Exception $e) {
            $this->log->write('Грешка при създаване на таблица customer_ban_ip: ' . $e->getMessage());
        }
    }
}
