<!-- Customers Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-800">Клиенти</h1>
			<p class="text-gray-500 mt-1">Управление на клиенти и техните данни</p>
		</div>
		<div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
			<a href="{{ add_url }}" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-add-line"></i>
				</div>
				<span>Нов клиент</span>
			</a>
		</div>
	</div>
</div>

<!-- Customer Filters -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
		<!-- Name Filter -->
		<div>
			<label class="block text-sm font-medium text-gray-700 mb-1">Име</label>
			<input type="text" id="filter-name" value="{{ filter_name }}" placeholder="Търсене по име..." 
				   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
		</div>

		<!-- Email Filter -->
		<div>
			<label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
			<input type="text" id="filter-email" value="{{ filter_email }}" placeholder="Търсене по email..." 
				   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
		</div>

		<!-- Customer Group Filter -->
		<div>
			<label class="block text-sm font-medium text-gray-700 mb-1">Клиентска група</label>
			<select id="filter-customer-group" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
				<option value="">Всички групи</option>
				{% for customer_group in customer_groups %}
					<option value="{{ customer_group.customer_group_id }}" {% if customer_group.customer_group_id == filter_customer_group_id %}selected{% endif %}>
						{{ customer_group.name }}
					</option>
				{% endfor %}
			</select>
		</div>

		<!-- Status Filter -->
		<div>
			<label class="block text-sm font-medium text-gray-700 mb-1">Статус</label>
			<select id="filter-status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
				<option value="">Всички</option>
				<option value="1" {% if filter_status == '1' %}selected{% endif %}>Активен</option>
				<option value="0" {% if filter_status == '0' %}selected{% endif %}>Неактивен</option>
			</select>
		</div>

		<!-- IP Filter -->
		<div>
			<label class="block text-sm font-medium text-gray-700 mb-1">IP адрес</label>
			<input type="text" id="filter-ip" value="{{ filter_ip }}" placeholder="IP адрес..." 
				   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
		</div>

		<!-- Date Filter -->
		<div>
			<label class="block text-sm font-medium text-gray-700 mb-1">Дата на регистрация</label>
			<input type="date" id="filter-date-added" value="{{ filter_date_added }}" 
				   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
		</div>
	</div>

	<!-- Filter Actions -->
	<div class="flex justify-between items-center mt-4">
		<div class="flex gap-2">
			<button type="button" id="apply-filters" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
				<i class="ri-search-line mr-2"></i>Филтрирай
			</button>
			<button type="button" id="clear-filters" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
				<i class="ri-close-line mr-2"></i>Изчисти
			</button>
		</div>
		<div class="text-sm text-gray-500">
			{{ results }}
		</div>
	</div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<!-- Customers List -->
	<div class="bg-white rounded shadow overflow-hidden">
		<!-- Bulk Actions -->
		<div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-4">
					<label class="flex items-center">
						<input type="checkbox" id="select-all" class="rounded border-gray-300 text-primary focus:ring-primary">
						<span class="ml-2 text-sm text-gray-700">Избери всички</span>
					</label>
					<div class="bulk-actions hidden">
						<button type="button" id="bulk-delete" class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors">
							<i class="ri-delete-bin-line mr-1"></i>Изтрий избраните
						</button>
					</div>
				</div>
				<div class="text-sm text-gray-500">
					Общо: {{ customer_total }} клиента
				</div>
			</div>
		</div>

		<!-- Customers Table -->
		<div class="overflow-x-auto">
			<table class="min-w-full divide-y divide-gray-200">
				<thead class="bg-gray-50">
					<tr>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
							<input type="checkbox" id="select-all-header" class="rounded border-gray-300 text-primary focus:ring-primary">
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="name">
							Име
							<i class="ri-arrow-up-down-line ml-1"></i>
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="email">
							Email
							<i class="ri-arrow-up-down-line ml-1"></i>
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
							Клиентска група
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="status">
							Статус
							<i class="ri-arrow-up-down-line ml-1"></i>
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
							IP адрес
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="date_added">
							Дата на регистрация
							<i class="ri-arrow-up-down-line ml-1"></i>
						</th>
						<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
							Действия
						</th>
					</tr>
				</thead>
				<tbody class="bg-white divide-y divide-gray-200">
					{% if customers %}
						{% for customer in customers %}
						<tr class="hover:bg-gray-50 transition-colors" data-customer-id="{{ customer.customer_id }}">
							<td class="px-6 py-4 whitespace-nowrap">
								<input type="checkbox" class="customer-checkbox rounded border-gray-300 text-primary focus:ring-primary" value="{{ customer.customer_id }}">
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="flex items-center">
									<div class="flex-shrink-0 h-10 w-10">
										<div class="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
											<i class="ri-user-line text-primary"></i>
										</div>
									</div>
									<div class="ml-4">
										<div class="text-sm font-medium text-gray-900">{{ customer.name }}</div>
									</div>
								</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="text-sm text-gray-900">{{ customer.email }}</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="text-sm text-gray-900">{{ customer.customer_group }}</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {% if customer.status == 'Активен' %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
									{{ customer.status }}
								</span>
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
								{{ customer.ip }}
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
								{{ customer.date_added }}
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
								<div class="flex items-center justify-end gap-2">
									<a href="{{ customer.edit }}" class="text-primary hover:text-primary/80 transition-colors" title="Редактирай">
										<i class="ri-edit-line"></i>
									</a>
									<button type="button" class="text-red-600 hover:text-red-800 transition-colors delete-customer" data-customer-id="{{ customer.customer_id }}" title="Изтрий">
										<i class="ri-delete-bin-line"></i>
									</button>
								</div>
							</td>
						</tr>
						{% endfor %}
					{% else %}
						<tr>
							<td colspan="8" class="px-6 py-12 text-center">
								<div class="text-gray-500">
									<i class="ri-user-line text-4xl mb-4"></i>
									<p class="text-lg font-medium">Няма намерени клиенти</p>
									<p class="text-sm">Опитайте да промените филтрите или добавете нов клиент</p>
								</div>
							</td>
						</tr>
					{% endif %}
				</tbody>
			</table>
		</div>

		<!-- Pagination -->
		{% if pagination %}
		<div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
			{{ pagination|raw }}
		</div>
		{% endif %}
	</div>
</main>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
	<div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
		<div class="mt-3 text-center">
			<div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
				<i class="ri-error-warning-line text-red-600 text-xl"></i>
			</div>
			<h3 class="text-lg font-medium text-gray-900 mt-4">Потвърждение за изтриване</h3>
			<div class="mt-2 px-7 py-3">
				<p class="text-sm text-gray-500" id="delete-message">
					Сигурни ли сте, че искате да изтриете избраните клиенти? Това действие не може да бъде отменено.
				</p>
			</div>
			<div class="items-center px-4 py-3">
				<button id="confirm-delete" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
					Изтрий
				</button>
				<button id="cancel-delete" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
					Отказ
				</button>
			</div>
		</div>
	</div>
</div>

<script>
// Конфигурация за страницата
window.customerConfig = {
	userToken: '{{ user_token }}',
	deleteUrl: '{{ delete_url }}',
	currentSort: '{{ sort }}',
	currentOrder: '{{ order }}'
};
</script>
