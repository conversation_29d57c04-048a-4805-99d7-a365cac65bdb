<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Transaction extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява управление на транзакции
     */
    public function execute() {
        $json = [];
        
        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за управление на транзакции!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            $action = $this->requestGet('action', '');
            
            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } else {
                $this->loadModelAs('customer/customer', 'customerModel');
                $customer_info = $this->customerModel->getCustomer($customer_id);
                
                if (!$customer_info) {
                    $json['error'] = 'Клиентът не съществува!';
                } else {
                    switch ($action) {
                        case 'add':
                            $json = $this->addTransaction($customer_id);
                            break;
                        case 'list':
                            $json = $this->getTransactions($customer_id);
                            break;
                        case 'delete':
                            $json = $this->deleteTransaction();
                            break;
                        default:
                            $json = $this->getTransactions($customer_id);
                            break;
                    }
                }
            }
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Добавя нова транзакция
     */
    private function addTransaction($customer_id) {
        $json = [];
        
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $amount = (float)$this->requestPost('amount', 0);
            $description = trim($this->requestPost('description', ''));
            
            if ($amount == 0) {
                $json['error'] = 'Сумата трябва да бъде различна от нула!';
            } elseif (empty($description)) {
                $json['error'] = 'Описанието е задължително!';
            } else {
                // Използваме стандартния OpenCart модел
                $this->loadModelAs('customer/customer', 'customerModel');

                $order_id = (int)$this->requestPost('order_id', 0);

                $this->customerModel->addTransaction($customer_id, $description, $amount, $order_id);
                
                // Логваме действието
                $this->loadModelAs('user/user', 'userModel');
                $user_info = $this->userModel->getUser($this->user->getId());
                
                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' добави транзакция за клиент ID: ' . $customer_id . ' - Сума: ' . $amount);
                }
                
                $json['success'] = 'Транзакцията беше успешно добавена!';
            }
        } else {
            $json['error'] = 'Невалидна заявка!';
        }
        
        return $json;
    }

    /**
     * Връща списък с транзакции
     */
    private function getTransactions($customer_id) {
        // Използваме стандартния OpenCart модел
        $this->loadModelAs('customer/customer', 'customerModel');

        $start = (int)$this->requestGet('start', 0);
        $limit = (int)$this->requestGet('limit', 20);

        $transactions = $this->customerModel->getTransactions($customer_id, $start, $limit);
        $total = $this->customerModel->getTotalTransactions($customer_id);
        
        $transaction_data = [];
        foreach ($transactions as $transaction) {
            $transaction_data[] = [
                'customer_transaction_id' => $transaction['customer_transaction_id'],
                'order_id' => $transaction['order_id'],
                'description' => $transaction['description'],
                'amount' => $this->currency->format($transaction['amount'], $this->getConfig('config_currency')),
                'date_added' => date('d.m.Y H:i', strtotime($transaction['date_added']))
            ];
        }
        
        return [
            'transactions' => $transaction_data,
            'total' => $total
        ];
    }

    /**
     * Изтрива транзакция
     */
    private function deleteTransaction() {
        $json = [];
        
        $transaction_id = (int)$this->requestGet('transaction_id', 0);
        
        if (!$transaction_id) {
            $json['error'] = 'Невалидна транзакция!';
        } else {
            $this->load->model('customer/customer_transaction');
            
            // Проверяваме дали транзакцията съществува
            $transaction_info = $this->model_customer_customer_transaction->getTransaction($transaction_id);
            
            if (!$transaction_info) {
                $json['error'] = 'Транзакцията не съществува!';
            } else {
                $this->model_customer_customer_transaction->deleteTransaction($transaction_id);
                
                // Логваме действието
                $this->load->model('user/user');
                $user_info = $this->model_user_user->getUser($this->user->getId());
                
                if ($user_info) {
                    $this->log->write('Администратор ' . $user_info['username'] . ' изтри транзакция ID: ' . $transaction_id);
                }
                
                $json['success'] = 'Транзакцията беше успешно изтрита!';
            }
        }
        
        return $json;
    }
}
