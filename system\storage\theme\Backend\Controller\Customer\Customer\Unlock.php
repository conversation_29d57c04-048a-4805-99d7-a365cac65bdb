<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Unlock extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява отключване на клиент
     */
    public function execute() {
        $json = [];
        
        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за отключване на клиенти!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            
            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } else {
                $this->load->model('customer/customer');
                $customer_info = $this->model_customer_customer->getCustomer($customer_id);
                
                if (!$customer_info) {
                    $json['error'] = 'Клиентът не съществува!';
                } else {
                    // Отключваме клиента като премахваме всички неуспешни опити за логин
                    // В OpenCart стандартната таблица се нарича customer_login, не customer_login_attempt
                    $this->db->query("DELETE FROM `" . DB_PREFIX . "customer_login` WHERE email = '" . $this->db->escape($customer_info['email']) . "'");

                    // Премахваме всички IP блокирания за този клиент (ако таблицата съществува)
                    $this->safeDeleteFromTable('customer_ip', "customer_id = '" . (int)$customer_id . "'");

                    // Премахваме IP адресите на клиента от ban списъка
                    $this->removeCustomerIpsFromBanList($customer_id);

                    // Активираме клиента ако е неактивен
                    if (!$customer_info['status']) {
                        $this->db->query("UPDATE `" . DB_PREFIX . "customer` SET status = '1' WHERE customer_id = '" . (int)$customer_id . "'");
                    }

                    // Одобряваме клиента ако не е одобрен
                    if (!$customer_info['approved']) {
                        $this->db->query("UPDATE `" . DB_PREFIX . "customer` SET approved = '1' WHERE customer_id = '" . (int)$customer_id . "'");
                    }
                    
                    // Логваме действието
                    $this->load->model('user/user');
                    $user_info = $this->model_user_user->getUser($this->user->getId());
                    
                    if ($user_info) {
                        $this->log->write('Администратор ' . $user_info['username'] . ' отключи клиент ' . $customer_info['email']);
                    }
                    
                    $json['success'] = 'Клиентът беше успешно отключен!';
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Безопасно изтрива записи от таблица, ако тя съществува
     */
    private function safeDeleteFromTable($tableName, $whereCondition) {
        try {
            // Проверяваме дали таблицата съществува
            $checkTable = $this->db->query("SHOW TABLES LIKE '" . DB_PREFIX . $tableName . "'");

            if ($checkTable->num_rows > 0) {
                $this->db->query("DELETE FROM `" . DB_PREFIX . $tableName . "` WHERE " . $whereCondition);
            }
        } catch (Exception $e) {
            // Логваме грешката, но не спираме изпълнението
            $this->log->write('Unlock: Грешка при изтриване от таблица ' . $tableName . ': ' . $e->getMessage());
        }
    }

    /**
     * Проверява дали таблица съществува в базата данни
     */
    private function tableExists($tableName) {
        try {
            $result = $this->db->query("SHOW TABLES LIKE '" . DB_PREFIX . $tableName . "'");
            return $result->num_rows > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Премахва IP адресите на клиента от ban списъка
     */
    private function removeCustomerIpsFromBanList($customer_id) {
        try {
            // Първо проверяваме дали таблиците съществуват
            if (!$this->tableExists('customer_ip') || !$this->tableExists('customer_ban_ip')) {
                return;
            }

            // Вземаме всички IP адреси на клиента
            $customerIps = $this->db->query("SELECT ip FROM `" . DB_PREFIX . "customer_ip` WHERE customer_id = '" . (int)$customer_id . "'");

            // Премахваме всеки IP от ban списъка
            foreach ($customerIps->rows as $row) {
                $this->db->query("DELETE FROM `" . DB_PREFIX . "customer_ban_ip` WHERE ip = '" . $this->db->escape($row['ip']) . "'");
            }
        } catch (Exception $e) {
            // Логваме грешката, но не спираме изпълнението
            $this->log->write('Unlock: Грешка при премахване на IP адреси от ban списъка: ' . $e->getMessage());
        }
    }
}
