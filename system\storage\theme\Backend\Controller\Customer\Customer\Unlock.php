<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Unlock extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява отключване на клиент
     */
    public function execute() {
        $json = [];
        
        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за отключване на клиенти!';
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            
            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } else {
                $this->load->model('customer/customer');
                $customer_info = $this->model_customer_customer->getCustomer($customer_id);
                
                if (!$customer_info) {
                    $json['error'] = 'Клиентът не съществува!';
                } else {
                    // Отключваме клиента като премахваме всички неуспешни опити за логин
                    $this->db->query("DELETE FROM `" . DB_PREFIX . "customer_login_attempt` WHERE email = '" . $this->db->escape($customer_info['email']) . "'");
                    
                    // Премахваме всички IP блокирания за този клиент
                    $this->db->query("DELETE FROM `" . DB_PREFIX . "customer_ip` WHERE customer_id = '" . (int)$customer_id . "'");
                    
                    // Активираме клиента ако е неактивен
                    if (!$customer_info['status']) {
                        $this->db->query("UPDATE `" . DB_PREFIX . "customer` SET status = '1' WHERE customer_id = '" . (int)$customer_id . "'");
                    }
                    
                    // Одобряваме клиента ако не е одобрен
                    if (!$customer_info['approved']) {
                        $this->db->query("UPDATE `" . DB_PREFIX . "customer` SET approved = '1' WHERE customer_id = '" . (int)$customer_id . "'");
                    }
                    
                    // Логваме действието
                    $this->load->model('user/user');
                    $user_info = $this->model_user_user->getUser($this->user->getId());
                    
                    if ($user_info) {
                        $this->log->write('Администратор ' . $user_info['username'] . ' отключи клиент ' . $customer_info['email']);
                    }
                    
                    $json['success'] = 'Клиентът беше успешно отключен!';
                }
            }
        }
        
        $this->setJSONResponseOutput($json);
    }
}
