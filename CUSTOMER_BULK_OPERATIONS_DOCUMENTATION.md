# 🔄 Customer Bulk Operations - Документация

## 📋 Обхват на промените

Добавени са два нови бутона за bulk операции в Customer модула на проекта Rakla.bg:
- **"Активирай избраните"** - активира избрани клиенти (status = 1)
- **"Деактивирай избраните"** - деактивира избрани клиенти (status = 0)

## ✅ Създадени/Модифицирани файлове

### 1. **Frontend (Twig шаблон)**

#### **customer.twig** ✅ МОДИФИЦИРАН
**Bulk Actions секция:**
```html
<div class="bulk-actions hidden">
    <div class="flex items-center gap-2">
        <button type="button" id="bulk-activate" class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors">
            <i class="ri-check-line mr-1"></i>Активирай избраните
        </button>
        <button type="button" id="bulk-deactivate" class="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700 transition-colors">
            <i class="ri-close-line mr-1"></i>Деактивирай избраните
        </button>
        <button type="button" id="bulk-delete" class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors">
            <i class="ri-delete-bin-line mr-1"></i>Изтрий избраните
        </button>
    </div>
</div>
```

**Нови модали:**
- ✅ **Activate Confirmation Modal** - зелен дизайн с check икона
- ✅ **Deactivate Confirmation Modal** - оранжев дизайн с close икона

**JavaScript конфигурация:**
```javascript
window.customerConfig = {
    userToken: '{{ user_token }}',
    deleteUrl: '{{ delete_url }}',
    activateUrl: '{{ activate_url }}',
    deactivateUrl: '{{ deactivate_url }}',
    currentSort: '{{ sort }}',
    currentOrder: '{{ order }}'
};
```

### 2. **Backend контролери**

#### **Customer.php** ✅ МОДИФИЦИРАН
**Нови dispatcher методи:**
```php
public function activate() {
    $subController = $this->setBackendSubController('Customer/Customer/Activate', $this);
    if ($subController) {
        return $subController->execute();
    } else {
        $this->jsonResponse(['error' => 'Sub-controller not found']);
    }
}

public function deactivate() {
    $subController = $this->setBackendSubController('Customer/Customer/Deactivate', $this);
    if ($subController) {
        return $subController->execute();
    } else {
        $this->jsonResponse(['error' => 'Sub-controller not found']);
    }
}
```

#### **Customer/Customer/Activate.php** ✅ СЪЗДАДЕН
**Основни функционалности:**
- ✅ Permission проверки (`modify` права за `customer/customer`)
- ✅ Валидация на избрани клиенти
- ✅ Bulk SQL операции за активиране (status = 1)
- ✅ Детайлно логване на действията
- ✅ Error handling и graceful degradation
- ✅ JSON response с статистики

**Ключови методи:**
```php
public function execute()                    // Главен метод за изпълнение
private function logBulkActivation()        // Логване на операцията
private function validateActivationRequest() // Валидация на заявката
private function customerExists()           // Проверка за съществуване
private function isCustomerActive()         // Проверка на статуса
private function activateCustomer()         // Активиране на единичен клиент
```

#### **Customer/Customer/Deactivate.php** ✅ СЪЗДАДЕН
**Основни функционалности:**
- ✅ Permission проверки (`modify` права за `customer/customer`)
- ✅ Валидация на избрани клиенти
- ✅ Bulk SQL операции за деактивиране (status = 0)
- ✅ Проверка за активни поръчки
- ✅ Детайлно логване на действията
- ✅ Error handling и graceful degradation
- ✅ JSON response с статистики

**Ключови методи:**
```php
public function execute()                      // Главен метод за изпълнение
private function logBulkDeactivation()        // Логване на операцията
private function validateDeactivationRequest() // Валидация на заявката
private function hasActiveOrders()            // Проверка за активни поръчки
private function getCustomerStats()           // Статистики за клиента
private function deactivateCustomer()         // Деактивиране на единичен клиент
```

#### **Customer/Customer/Index.php** ✅ МОДИФИЦИРАН
**Добавени URL-та:**
```php
$this->setData([
    'customer_groups' => $customer_groups,
    'add_url' => $this->getAdminLink('customer/customer/add'),
    'delete_url' => $this->getAdminLink('customer/customer/delete'),
    'activate_url' => $this->getAdminLink('customer/customer/activate'),      // НОВ
    'deactivate_url' => $this->getAdminLink('customer/customer/deactivate'),  // НОВ
    'user_token' => $this->session->data['user_token']
]);
```

### 3. **JavaScript модул**

#### **customer.js** ✅ РАЗШИРЕН
**Нови конфигурации:**
```javascript
config: {
    userToken: '',
    deleteUrl: '',
    activateUrl: '',      // НОВ
    deactivateUrl: '',    // НОВ
    currentSort: 'name',
    currentOrder: 'ASC',
    maxConcurrentRequests: 10,
    requestQueue: [],
    activeRequests: 0
}
```

**Нови DOM елементи:**
```javascript
elements: {
    // ... съществуващи елементи
    bulkActivate: null,           // НОВ
    bulkDeactivate: null,         // НОВ
    activateModal: null,          // НОВ
    deactivateModal: null,        // НОВ
    confirmActivate: null,        // НОВ
    confirmDeactivate: null,      // НОВ
    cancelActivate: null,         // НОВ
    cancelDeactivate: null,       // НОВ
    activateMessage: null,        // НОВ
    deactivateMessage: null       // НОВ
}
```

**Нови методи:**
```javascript
customer_handleBulkActivate()      // Обработка на bulk активиране
customer_handleBulkDeactivate()    // Обработка на bulk деактивиране
customer_confirmActivate()         // Потвърждение за активиране
customer_confirmDeactivate()       // Потвърждение за деактивиране
customer_performBulkOperation()    // Универсален метод за bulk операции
customer_showModal(type)           // Показване на модал по тип
customer_hideModal(type)           // Скриване на модал по тип
```

## 🎨 Дизайн и UX

### **Цветова схема:**
- **Активирай бутон:** `bg-green-600` с `hover:bg-green-700`
- **Деактивирай бутон:** `bg-orange-600` с `hover:bg-orange-700`
- **Изтрий бутон:** `bg-red-600` с `hover:bg-red-700` (съществуващ)

### **Икони (RemixIcon):**
- **Активирай:** `ri-check-line` (отметка)
- **Деактивирай:** `ri-close-line` (X)
- **Изтрий:** `ri-delete-bin-line` (кошче)

### **Responsive дизайн:**
- ✅ Mobile/tablet/desktop поддръжка
- ✅ Flex layout с gap за правилно подравняване
- ✅ Consistent Tailwind CSS стилове

### **Модали:**
- ✅ Цветни икони съответстващи на операцията
- ✅ Ясни съобщения с брой избрани клиенти
- ✅ Backdrop click и Escape key за затваряне
- ✅ Focus management за accessibility

## 🔒 Сигурност и валидация

### **Backend сигурност:**
- ✅ **Permission checks** - `hasPermission('modify', 'customer/customer')`
- ✅ **Input validation** - Проверка на POST данни и customer IDs
- ✅ **SQL injection protection** - Използване на prepared statements
- ✅ **Error handling** - Graceful degradation при грешки
- ✅ **Logging** - Детайлно логване на всички операции

### **Frontend валидация:**
- ✅ **Selection validation** - Проверка за избрани клиенти
- ✅ **Confirmation modals** - Потвърждение преди изпълнение
- ✅ **Error notifications** - Ясни съобщения за грешки
- ✅ **Loading states** - Индикация за процеса

## 📊 Функционални особености

### **Bulk операции:**
1. **Избор на клиенти** - чрез checkboxes
2. **Показване на бутоните** - само когато има избрани клиенти
3. **Confirmation modal** - с брой избрани клиенти
4. **AJAX изпълнение** - без page refresh
5. **Success notification** - с брой обработени клиенти
6. **Page refresh** - за показване на обновения статус

### **Error handling:**
- ✅ Невалидни customer IDs се пропускат
- ✅ Warnings за проблемни записи
- ✅ Graceful degradation при частични грешки
- ✅ Детайлни error messages

### **Logging:**
```php
// Пример log запис за активиране:
"Администратор admin активира 3 клиента: Иван Петров (<EMAIL>), Мария Георгиева (<EMAIL>), Петър Димитров (<EMAIL>)"
```

## 🚀 Архитектурни особености

### **Sub-controller pattern:**
- ✅ Следва същия pattern като Delete контролера
- ✅ Dispatcher методи в основния Customer контролер
- ✅ Отделни sub-контролери за всяка операция
- ✅ Консистентна error handling логика

### **JavaScript модулност:**
- ✅ Object.assign() за разширяване
- ✅ IIFE wrapper за encapsulation
- ✅ DOM caching за производителност
- ✅ Event delegation за динамични елементи

### **AJAX операции:**
- ✅ Request throttling (максимум 10 concurrent)
- ✅ Request queue за управление
- ✅ Error handling с retry логика
- ✅ Progress indication

## 📈 Резултат

### **Потребителски опит:**
- ✅ **Ефективност** - Bulk операции вместо единични
- ✅ **Яснота** - Цветни бутони и ясни съобщения
- ✅ **Сигурност** - Confirmation modals за всяка операция
- ✅ **Feedback** - Real-time notifications и статистики

### **Технически качества:**
- ✅ **Производителност** - Оптимизирани SQL заявки
- ✅ **Сигурност** - Comprehensive validation и logging
- ✅ **Поддръжка** - Модулна архитектура и clean code
- ✅ **Съвместимост** - Запазена съществуваща функционалност

---
**Дата на внедряването:** 2025-07-19  
**Статус:** ✅ Завършено и готово за production  
**Тестване:** Препоръчва се тестване с реални данни преди production deployment
