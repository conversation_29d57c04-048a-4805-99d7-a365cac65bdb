/**
 * Customer Approval Module
 * Handles customer approval operations, filtering, and bulk actions
 */
(function() {
    'use strict';

    const CustomerApprovalModule = {
        config: {
            userToken: '',
            approveUrl: '',
            denyUrl: '',
            currentSort: 'date_added',
            currentOrder: 'DESC',
            maxConcurrentRequests: 10,
            activeRequests: 0
        },

        elements: {
            selectAll: null,
            approvalCheckboxes: null,
            bulkActions: null,
            bulkApprove: null,
            bulkDeny: null,
            approvalModal: null,
            denyModal: null,
            confirmApproval: null,
            confirmDeny: null,
            cancelApproval: null,
            cancelDeny: null,
            approvalMessage: null,
            denyMessage: null,
            filterInputs: null,
            applyFilters: null,
            clearFilters: null,
            approveButtons: null,
            denyButtons: null
        },

        state: {
            selectedApprovals: new Set(),
            currentAction: null,
            isLoading: false
        },

        init() {
            this.customerApproval_loadConfig();
            this.customerApproval_cacheElements();
            this.customerApproval_bindEvents();
            this.customerApproval_initializeState();
            this.customerApproval_logDebug('Customer approval module initialized');
        },

        customerApproval_loadConfig() {
            if (window.customerApprovalConfig) {
                Object.assign(this.config, window.customerApprovalConfig);
            }
        },

        customerApproval_cacheElements() {
            this.elements.selectAll = document.getElementById('select-all');
            this.elements.approvalCheckboxes = document.querySelectorAll('.approval-checkbox');
            this.elements.bulkActions = document.querySelector('.bulk-actions');
            this.elements.bulkApprove = document.getElementById('bulk-approve');
            this.elements.bulkDeny = document.getElementById('bulk-deny');
            this.elements.approvalModal = document.getElementById('approval-modal');
            this.elements.denyModal = document.getElementById('deny-modal');
            this.elements.confirmApproval = document.getElementById('confirm-approval');
            this.elements.confirmDeny = document.getElementById('confirm-deny');
            this.elements.cancelApproval = document.getElementById('cancel-approval');
            this.elements.cancelDeny = document.getElementById('cancel-deny');
            this.elements.approvalMessage = document.getElementById('approval-message');
            this.elements.denyMessage = document.getElementById('deny-message');
            this.elements.filterInputs = {
                name: document.getElementById('filter-name'),
                email: document.getElementById('filter-email'),
                customerGroup: document.getElementById('filter-customer-group'),
                type: document.getElementById('filter-type'),
                dateAdded: document.getElementById('filter-date-added')
            };
            this.elements.applyFilters = document.getElementById('apply-filters');
            this.elements.clearFilters = document.getElementById('clear-filters');
            this.elements.approveButtons = document.querySelectorAll('.approve-customer');
            this.elements.denyButtons = document.querySelectorAll('.deny-customer');
        },

        customerApproval_bindEvents() {
            // Select all functionality
            if (this.elements.selectAll) {
                this.elements.selectAll.addEventListener('change', (e) => this.customerApproval_handleSelectAll(e));
            }

            // Individual checkboxes
            this.elements.approvalCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => this.customerApproval_handleCheckboxChange(e));
            });

            // Bulk actions
            if (this.elements.bulkApprove) {
                this.elements.bulkApprove.addEventListener('click', () => this.customerApproval_handleBulkApprove());
            }
            if (this.elements.bulkDeny) {
                this.elements.bulkDeny.addEventListener('click', () => this.customerApproval_handleBulkDeny());
            }

            // Modal actions
            if (this.elements.confirmApproval) {
                this.elements.confirmApproval.addEventListener('click', () => this.customerApproval_confirmApproval());
            }
            if (this.elements.confirmDeny) {
                this.elements.confirmDeny.addEventListener('click', () => this.customerApproval_confirmDeny());
            }
            if (this.elements.cancelApproval) {
                this.elements.cancelApproval.addEventListener('click', () => this.customerApproval_hideApprovalModal());
            }
            if (this.elements.cancelDeny) {
                this.elements.cancelDeny.addEventListener('click', () => this.customerApproval_hideDenyModal());
            }

            // Individual approve/deny buttons
            this.elements.approveButtons.forEach(button => {
                button.addEventListener('click', (e) => this.customerApproval_handleSingleApprove(e));
            });
            this.elements.denyButtons.forEach(button => {
                button.addEventListener('click', (e) => this.customerApproval_handleSingleDeny(e));
            });

            // Filter functionality
            if (this.elements.applyFilters) {
                this.elements.applyFilters.addEventListener('click', () => this.customerApproval_applyFilters());
            }
            if (this.elements.clearFilters) {
                this.elements.clearFilters.addEventListener('click', () => this.customerApproval_clearFilters());
            }

            // Enter key on filter inputs
            Object.values(this.elements.filterInputs).forEach(input => {
                if (input) {
                    input.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.customerApproval_applyFilters();
                        }
                    });
                }
            });
        },

        customerApproval_initializeState() {
            this.customerApproval_updateBulkActionsVisibility();
            this.customerApproval_loadCurrentFilters();
        },

        customerApproval_handleSelectAll(event) {
            const isChecked = event.target.checked;
            
            this.elements.approvalCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const approvalData = {
                    customerId: checkbox.value,
                    type: checkbox.dataset.type
                };
                
                if (isChecked) {
                    this.state.selectedApprovals.add(JSON.stringify(approvalData));
                } else {
                    this.state.selectedApprovals.delete(JSON.stringify(approvalData));
                }
            });

            this.customerApproval_updateBulkActionsVisibility();
        },

        customerApproval_handleCheckboxChange(event) {
            const approvalData = {
                customerId: event.target.value,
                type: event.target.dataset.type
            };
            const approvalKey = JSON.stringify(approvalData);
            const isChecked = event.target.checked;

            if (isChecked) {
                this.state.selectedApprovals.add(approvalKey);
            } else {
                this.state.selectedApprovals.delete(approvalKey);
            }

            // Update select all checkbox
            const allChecked = this.elements.approvalCheckboxes.length > 0 && 
                             Array.from(this.elements.approvalCheckboxes).every(cb => cb.checked);
            
            if (this.elements.selectAll) {
                this.elements.selectAll.checked = allChecked;
            }

            this.customerApproval_updateBulkActionsVisibility();
        },

        customerApproval_updateBulkActionsVisibility() {
            if (this.elements.bulkActions) {
                if (this.state.selectedApprovals.size > 0) {
                    this.elements.bulkActions.classList.remove('hidden');
                } else {
                    this.elements.bulkActions.classList.add('hidden');
                }
            }
        },

        customerApproval_handleBulkApprove() {
            if (this.state.selectedApprovals.size === 0) {
                this.customerApproval_showNotification('Моля изберете заявки за одобрение', 'warning');
                return;
            }

            const count = this.state.selectedApprovals.size;
            this.elements.approvalMessage.textContent = 
                `Сигурни ли сте, че искате да одобрите ${count} избрани заявки?`;
            
            this.customerApproval_showApprovalModal();
        },

        customerApproval_handleBulkDeny() {
            if (this.state.selectedApprovals.size === 0) {
                this.customerApproval_showNotification('Моля изберете заявки за отказ', 'warning');
                return;
            }

            const count = this.state.selectedApprovals.size;
            this.elements.denyMessage.textContent = 
                `Сигурни ли сте, че искате да откажете ${count} избрани заявки?`;
            
            this.customerApproval_showDenyModal();
        },

        customerApproval_handleSingleApprove(event) {
            const customerId = event.target.dataset.customerId;
            const type = event.target.dataset.type;
            
            this.state.selectedApprovals.clear();
            this.state.selectedApprovals.add(JSON.stringify({ customerId, type }));
            
            this.elements.approvalMessage.textContent = 
                'Сигурни ли сте, че искате да одобрите тази заявка?';
            
            this.customerApproval_showApprovalModal();
        },

        customerApproval_handleSingleDeny(event) {
            const customerId = event.target.dataset.customerId;
            const type = event.target.dataset.type;
            
            this.state.selectedApprovals.clear();
            this.state.selectedApprovals.add(JSON.stringify({ customerId, type }));
            
            this.elements.denyMessage.textContent = 
                'Сигурни ли сте, че искате да откажете тази заявка?';
            
            this.customerApproval_showDenyModal();
        },

        customerApproval_confirmApproval() {
            this.customerApproval_hideApprovalModal();
            this.customerApproval_performAction('approve');
        },

        customerApproval_confirmDeny() {
            this.customerApproval_hideDenyModal();
            this.customerApproval_performAction('deny');
        },

        customerApproval_performAction(action) {
            const url = action === 'approve' ? this.config.approveUrl : this.config.denyUrl;
            const approvals = Array.from(this.state.selectedApprovals).map(item => JSON.parse(item));
            
            const promises = approvals.map(approval => {
                const formData = new FormData();
                formData.append('customer_id', approval.customerId);
                formData.append('type', approval.type);
                formData.append('user_token', this.config.userToken);
                
                return fetch(url, {
                    method: 'POST',
                    body: formData
                }).then(response => response.json());
            });

            Promise.all(promises)
                .then(responses => this.customerApproval_handleActionResponse(responses, action))
                .catch(error => this.customerApproval_handleActionError(error));
        },

        customerApproval_handleActionResponse(responses, action) {
            const successCount = responses.filter(r => r.success).length;
            const errorCount = responses.filter(r => r.error).length;
            
            if (successCount > 0) {
                const actionText = action === 'approve' ? 'одобрени' : 'отказани';
                this.customerApproval_showNotification(`Успешно ${actionText} ${successCount} заявки`, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
            
            if (errorCount > 0) {
                this.customerApproval_showNotification(`${errorCount} заявки не можаха да бъдат обработени`, 'error');
            }
        },

        customerApproval_handleActionError(error) {
            this.customerApproval_logDebug('Action error:', error);
            this.customerApproval_showNotification('Възникна грешка при обработката', 'error');
        },

        customerApproval_applyFilters() {
            const filters = this.customerApproval_collectFilters();
            const url = new URL(window.location);
            
            Object.keys(filters).forEach(key => {
                url.searchParams.delete('filter_' + key);
            });
            
            Object.entries(filters).forEach(([key, value]) => {
                if (value) {
                    url.searchParams.set('filter_' + key, value);
                }
            });
            
            url.searchParams.delete('page');
            window.location.href = url.toString();
        },

        customerApproval_clearFilters() {
            Object.values(this.elements.filterInputs).forEach(input => {
                if (input) input.value = '';
            });
            
            const url = new URL(window.location);
            const filterParams = Array.from(url.searchParams.keys()).filter(key => key.startsWith('filter_'));
            filterParams.forEach(param => url.searchParams.delete(param));
            url.searchParams.delete('page');
            
            window.location.href = url.toString();
        },

        customerApproval_collectFilters() {
            return {
                name: this.elements.filterInputs.name?.value || '',
                email: this.elements.filterInputs.email?.value || '',
                customer_group_id: this.elements.filterInputs.customerGroup?.value || '',
                type: this.elements.filterInputs.type?.value || '',
                date_added: this.elements.filterInputs.dateAdded?.value || ''
            };
        },

        customerApproval_loadCurrentFilters() {
            const url = new URL(window.location);
            Object.entries(this.elements.filterInputs).forEach(([key, input]) => {
                if (input) {
                    const paramName = 'filter_' + (key === 'customerGroup' ? 'customer_group_id' : 
                                                   key === 'dateAdded' ? 'date_added' : key);
                    const value = url.searchParams.get(paramName);
                    if (value) input.value = value;
                }
            });
        },

        customerApproval_showApprovalModal() {
            if (this.elements.approvalModal) {
                this.elements.approvalModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        },

        customerApproval_hideApprovalModal() {
            if (this.elements.approvalModal) {
                this.elements.approvalModal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        },

        customerApproval_showDenyModal() {
            if (this.elements.denyModal) {
                this.elements.denyModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        },

        customerApproval_hideDenyModal() {
            if (this.elements.denyModal) {
                this.elements.denyModal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        },

        customerApproval_showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        customerApproval_logDebug(...args) {
            if (window.console && window.console.log) {
                console.log('[CustomerApprovalModule]', ...args);
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => CustomerApprovalModule.init());
    } else {
        CustomerApprovalModule.init();
    }

    // Expose module globally
    window.CustomerApprovalModule = CustomerApprovalModule;

})();
