<!-- Marketing Report -->
<div class="bg-white rounded-lg shadow-sm">
    <div class="border-b border-gray-200 px-6 py-4 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
            <i class="ri-megaphone-line mr-2 text-primary"></i>
            {{ heading_title }}
        </h3>
        <a href="{{ reset }}" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors text-sm">
            <i class="ri-refresh-line mr-2"></i>
            {{ button_reset }}
        </a>
    </div>
    
    <div class="p-6">
        <!-- Filter Section -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="filter_date_start" class="block text-sm font-medium text-gray-700 mb-1">{{ text_date_start }}</label>
                    <input type="date" id="filter_date_start" name="filter_date_start" value="{{ filter_date_start }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
                <div>
                    <label for="filter_date_end" class="block text-sm font-medium text-gray-700 mb-1">{{ text_date_end }}</label>
                    <input type="date" id="filter_date_end" name="filter_date_end" value="{{ filter_date_end }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
                <div>
                    <label for="filter_order_status_id" class="block text-sm font-medium text-gray-700 mb-1">{{ text_order_status }}</label>
                    <select id="filter_order_status_id" name="filter_order_status_id" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                        <option value="0">{{ text_all_status }}</option>
                        {% for order_status in order_statuses %}
                            {% if order_status.order_status_id == filter_order_status_id %}
                                <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                            {% else %}
                                <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="button" id="button-filter" class="w-full px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm">
                        <i class="ri-search-line mr-2"></i>
                        {{ button_filter }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Results Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_campaign }}
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_code }}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_clicks }}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_orders }}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_total }}
                        </th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_action }}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if marketings %}
                        {% for marketing in marketings %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <div class="font-medium">{{ marketing.campaign }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ marketing.code }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ marketing.clicks }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ marketing.orders }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-medium">
                                {{ marketing.total }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <a href="{{ marketing.action }}" class="text-primary hover:text-primary/80 text-sm font-medium">
                                    <i class="ri-edit-line mr-1"></i>
                                    {{ text_edit }}
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="ri-megaphone-line text-4xl mb-4"></i>
                                    <p class="text-lg font-medium">{{ text_no_results }}</p>
                                    <p class="text-sm">Няма маркетингови кампании за показване</p>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if pagination %}
        <div class="mt-6">
            <div class="pagination-container" style="position: relative; z-index: 1000;">
                {{ pagination|raw }}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics Cards -->
{% if marketings %}
<div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-6">
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                <i class="ri-megaphone-line text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Общо кампании</p>
                <p class="text-2xl font-bold text-gray-900">{{ marketings|length }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                <i class="ri-mouse-line text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Общо кликове</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% set total_clicks = 0 %}
                    {% for marketing in marketings %}
                        {% set total_clicks = total_clicks + marketing.clicks %}
                    {% endfor %}
                    {{ total_clicks }}
                </p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                <i class="ri-shopping-cart-line text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Общо поръчки</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% set total_orders = 0 %}
                    {% for marketing in marketings %}
                        {% set total_orders = total_orders + marketing.orders %}
                    {% endfor %}
                    {{ total_orders }}
                </p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100 text-orange-600 mr-4">
                <i class="ri-percent-line text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Конверсия</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% set total_clicks = 0 %}
                    {% set total_orders = 0 %}
                    {% for marketing in marketings %}
                        {% set total_clicks = total_clicks + marketing.clicks %}
                        {% set total_orders = total_orders + marketing.orders %}
                    {% endfor %}
                    {% if total_clicks > 0 %}
                        {{ ((total_orders / total_clicks) * 100)|number_format(1) }}%
                    {% else %}
                        0.0%
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    // Filter button functionality
    document.getElementById('button-filter').addEventListener('click', function() {
        var url = '';
        
        var filter_date_start = document.querySelector('input[name="filter_date_start"]').value;
        if (filter_date_start) {
            url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
        }
        
        var filter_date_end = document.querySelector('input[name="filter_date_end"]').value;
        if (filter_date_end) {
            url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
        }
        
        var filter_order_status_id = document.querySelector('select[name="filter_order_status_id"]').value;
        if (filter_order_status_id != 0) {
            url += '&filter_order_status_id=' + encodeURIComponent(filter_order_status_id);
        }
        
        window.location.href = 'index.php?route=report/report&code=marketing&user_token={{ user_token }}' + url;
    });
});
</script>

<style>
.pagination-container .dropdown-menu {
    position: absolute !important;
    z-index: 9999 !important;
    overflow: visible !important;
}

.pagination-container {
    overflow: visible !important;
}

.pagination-container .dropdown {
    position: relative;
    z-index: 1000;
}
</style>
