<!-- Customer Groups Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-800">Клиентски групи</h1>
			<p class="text-gray-500 mt-1">Управление на клиентски групи и техните настройки</p>
		</div>
		<div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
			<a href="{{ add_url }}" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-add-line"></i>
				</div>
				<span>Нова група</span>
			</a>
		</div>
	</div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<!-- Customer Groups List -->
	<div class="bg-white rounded shadow overflow-hidden">
		<!-- Bulk Actions -->
		<div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-4">
					<label class="flex items-center">
						<input type="checkbox" id="select-all" class="rounded border-gray-300 text-primary focus:ring-primary">
						<span class="ml-2 text-sm text-gray-700">Избери всички</span>
					</label>
					<div class="bulk-actions hidden">
						<button type="button" id="bulk-delete" class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors">
							<i class="ri-delete-bin-line mr-1"></i>Изтрий избраните
						</button>
					</div>
				</div>
				<div class="text-sm text-gray-500">
					Общо: {{ customer_group_total }} групи
				</div>
			</div>
		</div>

		<!-- Customer Groups Table -->
		<div class="overflow-x-auto">
			<table class="min-w-full divide-y divide-gray-200">
				<thead class="bg-gray-50">
					<tr>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
							<input type="checkbox" id="select-all-header" class="rounded border-gray-300 text-primary focus:ring-primary">
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="name">
							Име на групата
							<i class="ri-arrow-up-down-line ml-1"></i>
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
							Описание
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="approval">
							Изисква одобрение
							<i class="ri-arrow-up-down-line ml-1"></i>
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" data-sort="sort_order">
							Ред на сортиране
							<i class="ri-arrow-up-down-line ml-1"></i>
						</th>
						<th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
							Действия
						</th>
					</tr>
				</thead>
				<tbody class="bg-white divide-y divide-gray-200">
					{% if customer_groups %}
						{% for customer_group in customer_groups %}
						<tr class="hover:bg-gray-50 transition-colors" data-customer-group-id="{{ customer_group.customer_group_id }}">
							<td class="px-6 py-4 whitespace-nowrap">
								<input type="checkbox" class="customer-group-checkbox rounded border-gray-300 text-primary focus:ring-primary" value="{{ customer_group.customer_group_id }}">
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="flex items-center">
									<div class="flex-shrink-0 h-10 w-10">
										<div class="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
											<i class="ri-group-line text-primary"></i>
										</div>
									</div>
									<div class="ml-4">
										<div class="text-sm font-medium text-gray-900">{{ customer_group.name }}</div>
									</div>
								</div>
							</td>
							<td class="px-6 py-4">
								<div class="text-sm text-gray-900 max-w-xs truncate" title="{{ customer_group.description }}">
									{{ customer_group.description|default('Няма описание') }}
								</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {% if customer_group.approval == 'Да' %}bg-yellow-100 text-yellow-800{% else %}bg-green-100 text-green-800{% endif %}">
									{{ customer_group.approval }}
								</span>
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
								{{ customer_group.sort_order }}
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
								<div class="flex items-center justify-end gap-2">
									<a href="{{ customer_group.edit }}" class="text-primary hover:text-primary/80 transition-colors" title="Редактирай">
										<i class="ri-edit-line"></i>
									</a>
									<button type="button" class="text-red-600 hover:text-red-800 transition-colors delete-customer-group" data-customer-group-id="{{ customer_group.customer_group_id }}" title="Изтрий">
										<i class="ri-delete-bin-line"></i>
									</button>
								</div>
							</td>
						</tr>
						{% endfor %}
					{% else %}
						<tr>
							<td colspan="6" class="px-6 py-12 text-center">
								<div class="text-gray-500">
									<i class="ri-group-line text-4xl mb-4"></i>
									<p class="text-lg font-medium">Няма намерени клиентски групи</p>
									<p class="text-sm">Добавете първата клиентска група</p>
								</div>
							</td>
						</tr>
					{% endif %}
				</tbody>
			</table>
		</div>

		<!-- Pagination -->
		{% if pagination %}
		<div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
			{{ pagination|raw }}
		</div>
		{% endif %}
	</div>
</main>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
	<div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
		<div class="mt-3 text-center">
			<div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
				<i class="ri-error-warning-line text-red-600 text-xl"></i>
			</div>
			<h3 class="text-lg font-medium text-gray-900 mt-4">Потвърждение за изтриване</h3>
			<div class="mt-2 px-7 py-3">
				<p class="text-sm text-gray-500" id="delete-message">
					Сигурни ли сте, че искате да изтриете избраните клиентски групи? Това действие не може да бъде отменено.
				</p>
			</div>
			<div class="items-center px-4 py-3">
				<button id="confirm-delete" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300">
					Изтрий
				</button>
				<button id="cancel-delete" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
					Отказ
				</button>
			</div>
		</div>
	</div>
</div>

<script>
// Конфигурация за страницата
window.customerGroupConfig = {
	userToken: '{{ user_token }}',
	deleteUrl: '{{ delete_url }}',
	currentSort: '{{ sort }}',
	currentOrder: '{{ order }}'
};
</script>
