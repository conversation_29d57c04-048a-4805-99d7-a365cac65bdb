<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class History extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Показва историята на клиента
     */
    public function execute() {
        $customer_id = (int)$this->requestGet('customer_id', 0);
        
        if (!$customer_id) {
            return '';
        }

        $this->loadModelAs('customer/customer', 'customerModel');
        
        $histories = $this->customerModel->getHistories($customer_id);
        
        $data = [
            'histories' => $histories,
            'customer_id' => $customer_id,
            'user_token' => $this->session->data['user_token']
        ];

        return $this->load->view('customer/customer_history', $data);
    }

    /**
     * Добавя нова история за клиента
     */
    public function addhistory() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за добавяне на история!';
        } else {
            $customer_id = (int)$this->requestPost('customer_id', 0);
            $comment = trim($this->requestPost('comment', ''));

            if (!$customer_id) {
                $json['error'] = 'Невалиден клиент!';
            } elseif (empty($comment)) {
                $json['error'] = 'Коментарът е задължителен!';
            } else {
                $this->loadModelAs('customer/customer', 'customerModel');
                
                $this->customerModel->addHistory($customer_id, $comment);
                
                $json['success'] = 'Историята е добавена успешно!';
            }
        }

        $this->setJSONResponseOutput($json);
    }
}
