<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест на Unlock Customer функционалност</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Тест на Customer Unlock функционалност</h1>
    
    <div class="test-section info">
        <h3>Инструкции за тестване:</h3>
        <ol>
            <li>Отворете Developer Tools (F12) за да видите AJAX заявките</li>
            <li>Въведете валиден customer_id в полето по-долу</li>
            <li>Натиснете "Тест на таблици" за да проверите дали таблиците съществуват</li>
            <li>Натиснете "Създай таблици" ако някои липсват</li>
            <li>Натиснете "Unlock Customer" за да тествате функционалността</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>Настройки за тест:</h3>
        <label for="customer_id">Customer ID:</label>
        <input type="number" id="customer_id" value="1" min="1">
        <br><br>
        
        <label for="base_url">Base URL:</label>
        <input type="text" id="base_url" value="http://localhost/rakla/" style="width: 300px;">
        <br><br>
        
        <label for="user_token">User Token:</label>
        <input type="text" id="user_token" value="your_token_here" style="width: 400px;">
        <small>(Вземете от админ панела)</small>
    </div>

    <div class="test-section">
        <h3>Тестове:</h3>
        <button onclick="testTables()">Тест на таблици</button>
        <button onclick="createTables()">Създай таблици</button>
        <button onclick="unlockCustomer()">Unlock Customer</button>
        <button onclick="clearResults()">Изчисти резултати</button>
    </div>

    <div id="results"></div>

    <script>
        function getBaseUrl() {
            return document.getElementById('base_url').value.replace(/\/$/, '');
        }

        function getUserToken() {
            return document.getElementById('user_token').value;
        }

        function getCustomerId() {
            return document.getElementById('customer_id').value;
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `
                <h4>${title}</h4>
                <pre>${JSON.stringify(content, null, 2)}</pre>
                <small>Време: ${new Date().toLocaleString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testTables() {
            try {
                const url = `${getBaseUrl()}/admin/index.php?route=customer/customer/testtables&user_token=${getUserToken()}`;
                
                addResult('Заявка за тест на таблици', { url: url }, 'info');
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    addResult('Тест на таблици - Успех', data, 'success');
                } else {
                    addResult('Тест на таблици - Грешка', data, 'error');
                }
            } catch (error) {
                addResult('Тест на таблици - Network Error', { error: error.message }, 'error');
            }
        }

        async function createTables() {
            try {
                const url = `${getBaseUrl()}/admin/index.php?route=customer/customer/testtables/createtables&user_token=${getUserToken()}`;
                
                addResult('Заявка за създаване на таблици', { url: url }, 'info');
                
                const response = await fetch(url, { method: 'POST' });
                const data = await response.json();
                
                if (data.success || data.info) {
                    addResult('Създаване на таблици - Успех', data, 'success');
                } else {
                    addResult('Създаване на таблици - Грешка', data, 'error');
                }
            } catch (error) {
                addResult('Създаване на таблици - Network Error', { error: error.message }, 'error');
            }
        }

        async function unlockCustomer() {
            try {
                const customerId = getCustomerId();
                const url = `${getBaseUrl()}/admin/index.php?route=customer/customer/unlock&customer_id=${customerId}&user_token=${getUserToken()}`;
                
                addResult('Заявка за unlock на клиент', { 
                    url: url, 
                    customer_id: customerId 
                }, 'info');
                
                const response = await fetch(url, { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    addResult('Unlock Customer - Успех', data, 'success');
                } else {
                    addResult('Unlock Customer - Грешка', data, 'error');
                }
            } catch (error) {
                addResult('Unlock Customer - Network Error', { error: error.message }, 'error');
            }
        }

        // Автоматично зареждане на настройките от localStorage
        window.onload = function() {
            const savedBaseUrl = localStorage.getItem('test_base_url');
            const savedToken = localStorage.getItem('test_user_token');
            const savedCustomerId = localStorage.getItem('test_customer_id');
            
            if (savedBaseUrl) document.getElementById('base_url').value = savedBaseUrl;
            if (savedToken) document.getElementById('user_token').value = savedToken;
            if (savedCustomerId) document.getElementById('customer_id').value = savedCustomerId;
        };

        // Запазване на настройките в localStorage
        document.getElementById('base_url').addEventListener('change', function() {
            localStorage.setItem('test_base_url', this.value);
        });
        
        document.getElementById('user_token').addEventListener('change', function() {
            localStorage.setItem('test_user_token', this.value);
        });
        
        document.getElementById('customer_id').addEventListener('change', function() {
            localStorage.setItem('test_customer_id', this.value);
        });
    </script>
</body>
</html>
