<?php

namespace Theme25\Backend\Controller\Report\Report;

class SaleShipping extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на необходимите модели
        $this->loadModel('extension/report/sale');
        $this->loadModel('localisation/order_status');
    }

    /**
     * Генерира отчета за доставки
     */
    public function report() {
        // Получаваме филтрите - същата логика като в оригиналния код
        $filter_date_start = $this->requestGet('filter_date_start', '');
        $filter_date_end = $this->requestGet('filter_date_end', '');
        $filter_group = $this->requestGet('filter_group', 'week');
        $filter_order_status_id = $this->requestGet('filter_order_status_id', 0);
        $filter_limit = $this->requestGet('limit', 20);
        $page = $this->requestGet('page', 1);

        // Подготвяме данните за заявката - същата логика като в оригиналния код
        $filter_data = [
            'filter_date_start'      => $filter_date_start,
            'filter_date_end'        => $filter_date_end,
            'filter_group'           => $filter_group,
            'filter_order_status_id' => $filter_order_status_id,
            'start'                  => ($page - 1) * $filter_limit,
            'limit'                  => $filter_limit
        ];

        // Получаваме данните от модела
        $order_total = $this->model_extension_report_sale->getTotalShipping($filter_data);
        $results = $this->model_extension_report_sale->getShipping($filter_data);

        // Подготвяме данните за изгледа
        $orders = [];
        foreach ($results as $result) {
            $orders[] = [
                'date_start' => date('d.m.Y', strtotime($result['date_start'])),
                'date_end'   => date('d.m.Y', strtotime($result['date_end'])),
                'title'      => $result['title'],
                'orders'     => $result['orders'],
                'total'      => $this->formatCurrency($result['total'])
            ];
        }

        // Зареждаме статусите на поръчките
        $order_statuses = $this->model_localisation_order_status->getOrderStatuses();

        // Подготвяме групите за филтриране
        $groups = [
            ['text' => 'Година', 'value' => 'year'],
            ['text' => 'Месец', 'value' => 'month'],
            ['text' => 'Седмица', 'value' => 'week'],
            ['text' => 'Ден', 'value' => 'day']
        ];

        // Пагинация
        $pagination_data = $this->preparePagination($order_total, $page, $filter_limit, [
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'filter_group' => $filter_group,
            'filter_order_status_id' => $filter_order_status_id,
            'limit' => $filter_limit
        ]);

        // Подготвяме данните за шаблона
        $data = [
            'orders' => $orders,
            'order_statuses' => $order_statuses,
            'groups' => $groups,
            'pagination' => $pagination_data['pagination'],
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'filter_group' => $filter_group,
            'filter_order_status_id' => $filter_order_status_id
        ];

        // Добавяме текстовете за шаблона
        $data = array_merge($data, [
            'heading_title' => 'Отчет за доставки',
            'button_filter' => 'Филтър',
            'button_reset' => 'Нулиране',
            'text_date_start' => 'Начална дата',
            'text_date_end' => 'Крайна дата',
            'text_group' => 'Групиране по',
            'text_order_status' => 'Статус на поръчката',
            'text_all_status' => 'Всички статуси',
            'column_date_start' => 'Начална дата',
            'column_date_end' => 'Крайна дата',
            'column_title' => 'Метод на доставка',
            'column_orders' => 'Поръчки',
            'column_total' => 'Общо',
            'text_no_results' => 'Няма данни за показване',
            'reset' => $this->getAdminLink('report/report', 'code=sale_shipping')
        ]);

        // Рендираме адаптирания шаблон
        return $this->renderPartTemplate('report/sale_shipping_info', $data);
    }

    /**
     * Подготвя пагинацията за отчета
     *
     * @param int $total Общ брой записи
     * @param int $page Текуща страница
     * @param int $limit Записи на страница
     * @param array $filters Филтри за URL
     * @return array Масив с pagination HTML и results текст
     */
    private function preparePagination($total, $page, $limit, $filters = []) {
        // Опции за брой записи на страница
        $limits = [
            ['value' => 10, 'text' => '10 на страница'],
            ['value' => 20, 'text' => '20 на страница'],
            ['value' => 50, 'text' => '50 на страница'],
            ['value' => 100, 'text' => '100 на страница']
        ];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildReportFilterParams($filters);
        $pagination->url = $this->getAdminLink('report/report', 'code=sale_shipping' . $filter_params . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('report/report', 'code=sale_shipping' . $filter_params . '&limit={limit}'));
        $pagination->setProductText('записа');

        return [
            'pagination' => $pagination->render()
        ];
    }

    /**
     * Генерира URL параметри за филтрите на отчета
     *
     * @param array $filters Данни за филтрите
     * @return string URL параметри
     */
    private function buildReportFilterParams($filters) {
        $params = [];

        foreach ($filters as $key => $value) {
            if ($value !== '' && $value !== null && $value !== 0) {
                $params[] = $key . '=' . urlencode($value);
            }
        }

        return $params ? '&' . implode('&', $params) : '';
    }
}
