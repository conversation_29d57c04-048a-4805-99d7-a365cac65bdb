<!-- Customer Transaction Report -->
<div class="bg-white rounded-lg shadow-sm">
    <div class="border-b border-gray-200 px-6 py-4 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
            <i class="ri-exchange-line mr-2 text-primary"></i>
            {{ heading_title }}
        </h3>
        <a href="{{ reset }}" class="px-4 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors text-sm">
            <i class="ri-refresh-line mr-2"></i>
            {{ button_reset }}
        </a>
    </div>
    
    <div class="p-6">
        <!-- Filter Section -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="filter_date_start" class="block text-sm font-medium text-gray-700 mb-1">{{ text_date_start }}</label>
                    <input type="date" id="filter_date_start" name="filter_date_start" value="{{ filter_date_start }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
                <div>
                    <label for="filter_date_end" class="block text-sm font-medium text-gray-700 mb-1">{{ text_date_end }}</label>
                    <input type="date" id="filter_date_end" name="filter_date_end" value="{{ filter_date_end }}" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
                <div>
                    <label for="filter_customer" class="block text-sm font-medium text-gray-700 mb-1">{{ text_customer }}</label>
                    <input type="text" id="filter_customer" name="filter_customer" value="{{ filter_customer }}" placeholder="Име на клиент"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm">
                </div>
                <div class="flex items-end">
                    <button type="button" id="button-filter" class="w-full px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors text-sm">
                        <i class="ri-search-line mr-2"></i>
                        {{ button_filter }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Results Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_customer }}
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_email }}
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_customer_group }}
                        </th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_status }}
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_total }}
                        </th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column_action }}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if customers %}
                        {% for customer in customers %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <div class="font-medium">{{ customer.customer }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                {{ customer.email }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                {{ customer.customer_group }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                {% if customer.status == 'Активен' %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ customer.status }}
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        {{ customer.status }}
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-medium">
                                {{ customer.total }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <a href="{{ customer.edit }}" class="text-primary hover:text-primary/80 text-sm font-medium">
                                    <i class="ri-edit-line mr-1"></i>
                                    {{ text_edit }}
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="ri-exchange-line text-4xl mb-4"></i>
                                    <p class="text-lg font-medium">{{ text_no_results }}</p>
                                    <p class="text-sm">Няма транзакции на клиенти за показване</p>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if pagination %}
        <div class="mt-6">
            <div class="pagination-container" style="position: relative; z-index: 1000;">
                {{ pagination|raw }}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Statistics Cards -->
{% if customers %}
<div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                <i class="ri-user-line text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Общо клиенти</p>
                <p class="text-2xl font-bold text-gray-900">{{ customers|length }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                <i class="ri-exchange-line text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Активни клиенти</p>
                <p class="text-2xl font-bold text-gray-900">
                    {% set active_customers = 0 %}
                    {% for customer in customers %}
                        {% if customer.status == 'Активен' %}
                            {% set active_customers = active_customers + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ active_customers }}
                </p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                <i class="ri-money-dollar-circle-line text-xl"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Средна стойност</p>
                <p class="text-lg font-semibold text-gray-900">
                    {% if customers|length > 0 %}
                        Варира
                    {% else %}
                        0.00 лв.
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    // Filter button functionality
    document.getElementById('button-filter').addEventListener('click', function() {
        var url = '';
        
        var filter_date_start = document.querySelector('input[name="filter_date_start"]').value;
        if (filter_date_start) {
            url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
        }
        
        var filter_date_end = document.querySelector('input[name="filter_date_end"]').value;
        if (filter_date_end) {
            url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
        }
        
        var filter_customer = document.querySelector('input[name="filter_customer"]').value;
        if (filter_customer) {
            url += '&filter_customer=' + encodeURIComponent(filter_customer);
        }
        
        window.location.href = 'index.php?route=report/report&code=customer_transaction&user_token={{ user_token }}' + url;
    });
});
</script>

<style>
.pagination-container .dropdown-menu {
    position: absolute !important;
    z-index: 9999 !important;
    overflow: visible !important;
}

.pagination-container {
    overflow: visible !important;
}

.pagination-container .dropdown {
    position: relative;
    z-index: 1000;
}
</style>
