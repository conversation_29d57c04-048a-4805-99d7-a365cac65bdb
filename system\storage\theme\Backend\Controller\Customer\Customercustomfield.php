<?php

namespace Theme25\Backend\Controller\Customer;

class Customercustomfield extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'customer/custom_field');
    }

    /**
     * Главна страница с персонализирани полета за клиенти - dispatcher метод
     */
    public function index() {
        $subController = $this->setBackendSubController('Customer/Customercustomfield/Index', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Персонализирани полета за клиенти');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/custom_field');
        }
    }

    /**
     * Добавяне на ново персонализирано поле - dispatcher метод
     */
    public function add() {
        $subController = $this->setBackendSubController('Customer/Customercustomfield/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Добавяне на персонализирано поле');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/custom_field_form');
        }
    }

    /**
     * Редактиране на персонализирано поле - dispatcher метод
     */
    public function edit() {
        $subController = $this->setBackendSubController('Customer/Customercustomfield/Edit', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Редактиране на персонализирано поле');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/custom_field_form');
        }
    }

    /**
     * Запазване на персонализирано поле - dispatcher метод
     */
    public function save() {
        $subController = $this->setBackendSubController('Customer/Customercustomfield/Save', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Изтриване на персонализирано поле - dispatcher метод
     */
    public function delete() {
        $subController = $this->setBackendSubController('Customer/Customercustomfield/Delete', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * AJAX методи за автокомплийт и други динамични операции
     */
    public function autocomplete() {
        $json = [];
        
        if ($this->requestGet('type')) {
            $type = $this->requestGet('type');
            
            // Динамично зареждане на суб-контролер
            $sub_controller = $this->setBackendSubController('Customer/Customercustomfield/' . ucfirst($type) . 'Autocomplete', $this);
            
            if ($sub_controller && is_callable([$sub_controller, 'autocomplete'])) {
                $json = $sub_controller->autocomplete($this->requestGet());
            }
        }
        
        $this->setJSONResponseOutput($json);
    }
}
