<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Index extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $base = $this->getServer('HTTPS') ? HTTPS_CATALOG : HTTP_CATALOG;
        
        // Зареждаме customer.js, ако съществува
        $customerJsUrl = $base . 'backend_js/customer.js';
        $customerJsPath = DIR_THEME . 'Backend/View/Javascript/customer.js';
        
        if (file_exists($customerJsPath)) {
            $lastModified = filemtime($customerJsPath);
            $customerJsUrl .= '?v=' . $lastModified;
            $this->document->addScript($customerJsUrl, 'footer');
        }
    }

    /**
     * Изпълнява подготовката на данните за списъка с клиенти
     */
    public function execute() {
        $this->setTitle('Клиенти');
        $this->initAdminData();
        $this->prepareData();
        $this->renderTemplateWithDataAndOutput('customer/customer');
    }

    /**
     * Подготвя данните за списъка с клиенти
     */
    public function prepareData() {
        $this->prepareFilterData()
             ->prepareCustomerData()
             ->preparePagination()
             ->prepareAdditionalData();

        return $this;
    }

    /**
     * Подготвя данните за филтриране
     */
    private function prepareFilterData() {
        // Филтри от GET заявката
        $filter_name = $this->requestGet('filter_name', '');
        $filter_email = $this->requestGet('filter_email', '');
        $filter_customer_group_id = $this->requestGet('filter_customer_group_id', '');
        $filter_status = $this->requestGet('filter_status', '');
        $filter_ip = $this->requestGet('filter_ip', '');
        $filter_date_added = $this->requestGet('filter_date_added', '');

        // Сортиране и подредба
        $sort = $this->requestGet('sort', 'name');
        $order = $this->requestGet('order', 'ASC');
        $page = max(1, (int)$this->requestGet('page', 1));

        $this->setData([
            'filter_name' => $filter_name,
            'filter_email' => $filter_email,
            'filter_customer_group_id' => $filter_customer_group_id,
            'filter_status' => $filter_status,
            'filter_ip' => $filter_ip,
            'filter_date_added' => $filter_date_added,
            'sort' => $sort,
            'order' => $order,
            'page' => $page
        ]);

        return $this;
    }

    /**
     * Подготвя данните за клиентите
     */
    private function prepareCustomerData() {
        $this->load->model('customer/customer');
        $this->load->model('customer/customer_group');

        $filter_data = [
            'filter_name' => $this->data['filter_name'],
            'filter_email' => $this->data['filter_email'],
            'filter_customer_group_id' => $this->data['filter_customer_group_id'],
            'filter_status' => $this->data['filter_status'],
            'filter_ip' => $this->data['filter_ip'],
            'filter_date_added' => $this->data['filter_date_added'],
            'sort' => $this->data['sort'],
            'order' => $this->data['order'],
            'start' => ($this->data['page'] - 1) * $this->getConfig('config_limit_admin'),
            'limit' => $this->getConfig('config_limit_admin')
        ];

        $customer_total = $this->model_customer_customer->getTotalCustomers($filter_data);
        $results = $this->model_customer_customer->getCustomers($filter_data);

        $customers = [];
        foreach ($results as $result) {
            $customers[] = [
                'customer_id' => $result['customer_id'],
                'name' => $result['name'],
                'email' => $result['email'],
                'customer_group' => $result['customer_group'],
                'status' => $result['status'] ? 'Активен' : 'Неактивен',
                'ip' => $result['ip'],
                'date_added' => date('d.m.Y', strtotime($result['date_added'])),
                'edit' => $this->getAdminLink('customer/customer/edit', 'customer_id=' . $result['customer_id']),
                'delete' => $this->getAdminLink('customer/customer/delete', 'customer_id=' . $result['customer_id'])
            ];
        }

        $this->setData([
            'customers' => $customers,
            'customer_total' => $customer_total
        ]);

        return $this;
    }

    /**
     * Подготвя пагинацията
     */
    private function preparePagination() {
        $limit = $this->getConfig('config_limit_admin');

        // Подготвяне на лимитите за dropdown
        $limits = [10, 20, 50, 100];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $this->data['customer_total'];
        $pagination->page = $this->data['page'];
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildFilterParams();
        $pagination->url = $this->getAdminLink('customer/customer', $filter_params . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('customer/customer', $filter_params . '&limit={limit}'));
        $pagination->setProductText('клиенти');

        $this->setData([
            'pagination' => $pagination->render(),
            'results' => sprintf('Показани %d до %d от %d (%d страници)', 
                ($this->data['page'] - 1) * $limit + 1, 
                min($this->data['customer_total'], $this->data['page'] * $limit), 
                $this->data['customer_total'], 
                ceil($this->data['customer_total'] / $limit)
            )
        ]);

        return $this;
    }

    /**
     * Подготвя допълнителни данни
     */
    private function prepareAdditionalData() {
        // Зареждане на клиентските групи за филтъра
        $this->load->model('customer/customer_group');
        $customer_groups = $this->model_customer_customer_group->getCustomerGroups();

        $this->setData([
            'customer_groups' => $customer_groups,
            'add_url' => $this->getAdminLink('customer/customer/add'),
            'delete_url' => $this->getAdminLink('customer/customer/delete'),
            'user_token' => $this->session->data['user_token']
        ]);

        return $this;
    }

    /**
     * Генерира параметрите за филтриране в URL
     */
    private function buildFilterParams() {
        $params = [];

        if (!empty($this->data['filter_name'])) {
            $params[] = 'filter_name=' . urlencode($this->data['filter_name']);
        }

        if (!empty($this->data['filter_email'])) {
            $params[] = 'filter_email=' . urlencode($this->data['filter_email']);
        }

        if (!empty($this->data['filter_customer_group_id'])) {
            $params[] = 'filter_customer_group_id=' . $this->data['filter_customer_group_id'];
        }

        if (!empty($this->data['filter_status'])) {
            $params[] = 'filter_status=' . $this->data['filter_status'];
        }

        if (!empty($this->data['filter_ip'])) {
            $params[] = 'filter_ip=' . urlencode($this->data['filter_ip']);
        }

        if (!empty($this->data['filter_date_added'])) {
            $params[] = 'filter_date_added=' . urlencode($this->data['filter_date_added']);
        }

        if (!empty($this->data['sort'])) {
            $params[] = 'sort=' . $this->data['sort'];
        }

        if (!empty($this->data['order'])) {
            $params[] = 'order=' . $this->data['order'];
        }

        return implode('&', $params);
    }
}
