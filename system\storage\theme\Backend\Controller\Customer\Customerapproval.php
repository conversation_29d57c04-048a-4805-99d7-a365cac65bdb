<?php

namespace Theme25\Backend\Controller\Customer;

class Customerapproval extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'customer/customer_approval');
    }

    /**
     * Главна страница с одобрения на клиенти - dispatcher метод
     */
    public function index() {
        $subController = $this->setBackendSubController('Customer/Customerapproval/Index', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->setTitle('Одобрения на клиенти');
            $this->initAdminData();
            $this->renderTemplateWithDataAndOutput('customer/customer_approval');
        }
    }

    /**
     * Одобряване на клиент - dispatcher метод
     */
    public function approve() {
        $subController = $this->setBackendSubController('Customer/Customerapproval/Approve', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * Отказване на клиент - dispatcher метод
     */
    public function deny() {
        $subController = $this->setBackendSubController('Customer/Customerapproval/Deny', $this);
        if ($subController) {
            return $subController->execute();
        } else {
            $this->jsonResponse(['error' => 'Sub-controller not found']);
        }
    }

    /**
     * AJAX методи за автокомплийт и други динамични операции
     */
    public function autocomplete() {
        $json = [];
        
        if ($this->requestGet('type')) {
            $type = $this->requestGet('type');
            
            // Динамично зареждане на суб-контролер
            $sub_controller = $this->setBackendSubController('Customer/Customerapproval/' . ucfirst($type) . 'Autocomplete', $this);
            
            if ($sub_controller && is_callable([$sub_controller, 'autocomplete'])) {
                $json = $sub_controller->autocomplete($this->requestGet());
            }
        }
        
        $this->setJSONResponseOutput($json);
    }
}
