/**
 * Custom Field Module
 * Handles custom field listing, form management, and field value operations
 */
(function() {
    'use strict';

    const CustomFieldModule = {
        config: {
            userToken: '',
            deleteUrl: '',
            currentSort: 'sort_order',
            currentOrder: 'ASC',
            maxConcurrentRequests: 10,
            activeRequests: 0,
            languages: [],
            customFieldValue: []
        },

        elements: {
            selectAll: null,
            fieldCheckboxes: null,
            bulkActions: null,
            bulkDelete: null,
            deleteModal: null,
            confirmDelete: null,
            cancelDelete: null,
            deleteMessage: null,
            sortHeaders: null,
            deleteButtons: null,
            // Form elements
            fieldTypeSelect: null,
            fieldValuesSection: null,
            fieldValuesContainer: null,
            addFieldValueBtn: null,
            langTabs: null,
            langContents: null
        },

        state: {
            selectedFields: new Set(),
            fieldValueIndex: 0,
            currentFieldType: 'text',
            isLoading: false
        },

        init() {
            this.customField_loadConfig();
            this.customField_cacheElements();
            this.customField_bindEvents();
            this.customField_initializeState();
            this.customField_logDebug('Custom field module initialized');
        },

        customField_loadConfig() {
            if (window.customFieldConfig) {
                Object.assign(this.config, window.customFieldConfig);
            }
            if (window.customFieldFormConfig) {
                Object.assign(this.config, window.customFieldFormConfig);
            }
        },

        customField_cacheElements() {
            // List page elements
            this.elements.selectAll = document.getElementById('select-all');
            this.elements.fieldCheckboxes = document.querySelectorAll('.custom-field-checkbox');
            this.elements.bulkActions = document.querySelector('.bulk-actions');
            this.elements.bulkDelete = document.getElementById('bulk-delete');
            this.elements.deleteModal = document.getElementById('delete-modal');
            this.elements.confirmDelete = document.getElementById('confirm-delete');
            this.elements.cancelDelete = document.getElementById('cancel-delete');
            this.elements.deleteMessage = document.getElementById('delete-message');
            this.elements.sortHeaders = document.querySelectorAll('[data-sort]');
            this.elements.deleteButtons = document.querySelectorAll('.delete-custom-field');

            // Form page elements
            this.elements.fieldTypeSelect = document.getElementById('field-type');
            this.elements.fieldValuesSection = document.getElementById('field-values-section');
            this.elements.fieldValuesContainer = document.getElementById('field-values-container');
            this.elements.addFieldValueBtn = document.getElementById('add-field-value');
            this.elements.langTabs = document.querySelectorAll('.lang-tab');
            this.elements.langContents = document.querySelectorAll('.lang-content');
        },

        customField_bindEvents() {
            // List page events
            this.customField_bindListEvents();
            
            // Form page events
            this.customField_bindFormEvents();
        },

        customField_bindListEvents() {
            // Select all functionality
            if (this.elements.selectAll) {
                this.elements.selectAll.addEventListener('change', (e) => this.customField_handleSelectAll(e));
            }

            // Individual checkboxes
            this.elements.fieldCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => this.customField_handleCheckboxChange(e));
            });

            // Bulk delete
            if (this.elements.bulkDelete) {
                this.elements.bulkDelete.addEventListener('click', () => this.customField_handleBulkDelete());
            }

            // Delete modal
            if (this.elements.confirmDelete) {
                this.elements.confirmDelete.addEventListener('click', () => this.customField_confirmDelete());
            }
            if (this.elements.cancelDelete) {
                this.elements.cancelDelete.addEventListener('click', () => this.customField_hideModal());
            }

            // Sort headers
            this.elements.sortHeaders.forEach(header => {
                header.addEventListener('click', (e) => this.customField_handleSort(e));
            });

            // Individual delete buttons
            this.elements.deleteButtons.forEach(button => {
                button.addEventListener('click', (e) => this.customField_handleSingleDelete(e));
            });

            // Modal backdrop and escape key
            if (this.elements.deleteModal) {
                this.elements.deleteModal.addEventListener('click', (e) => {
                    if (e.target === this.elements.deleteModal) {
                        this.customField_hideModal();
                    }
                });
            }

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && !this.elements.deleteModal?.classList.contains('hidden')) {
                    this.customField_hideModal();
                }
            });
        },

        customField_bindFormEvents() {
            // Field type change
            if (this.elements.fieldTypeSelect) {
                this.elements.fieldTypeSelect.addEventListener('change', (e) => this.customField_handleFieldTypeChange(e));
            }

            // Add field value
            if (this.elements.addFieldValueBtn) {
                this.elements.addFieldValueBtn.addEventListener('click', () => this.customField_addFieldValue());
            }

            // Remove field value (delegated)
            if (this.elements.fieldValuesContainer) {
                this.elements.fieldValuesContainer.addEventListener('click', (e) => {
                    if (e.target.closest('.remove-field-value')) {
                        this.customField_removeFieldValue(e);
                    }
                });
            }

            // Language tabs
            this.elements.langTabs.forEach(tab => {
                tab.addEventListener('click', (e) => this.customField_switchLanguageTab(e));
            });
        },

        customField_initializeState() {
            this.customField_updateBulkActionsVisibility();
            
            // Initialize form state
            if (this.elements.fieldTypeSelect) {
                this.state.currentFieldType = this.elements.fieldTypeSelect.value;
                this.customField_updateFieldValuesVisibility();
            }

            // Set initial field value index
            if (this.config.customFieldValue && this.config.customFieldValue.length > 0) {
                this.state.fieldValueIndex = this.config.customFieldValue.length;
            }
        },

        customField_handleSelectAll(event) {
            const isChecked = event.target.checked;

            this.elements.fieldCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const fieldId = checkbox.value;

                if (isChecked) {
                    this.state.selectedFields.add(fieldId);
                } else {
                    this.state.selectedFields.delete(fieldId);
                }
            });

            this.customField_updateBulkActionsVisibility();
        },

        customField_handleCheckboxChange(event) {
            const fieldId = event.target.value;
            const isChecked = event.target.checked;

            if (isChecked) {
                this.state.selectedFields.add(fieldId);
            } else {
                this.state.selectedFields.delete(fieldId);
            }

            // Update select all checkbox
            const allChecked = this.elements.fieldCheckboxes.length > 0 &&
                             Array.from(this.elements.fieldCheckboxes).every(cb => cb.checked);

            if (this.elements.selectAll) {
                this.elements.selectAll.checked = allChecked;
            }

            this.customField_updateBulkActionsVisibility();
        },

        customField_handleSingleDelete(event) {
            const fieldId = event.target.closest('[data-custom-field-id]').dataset.customFieldId;
            this.state.selectedFields.clear();
            this.state.selectedFields.add(fieldId);

            this.elements.deleteMessage.textContent =
                'Сигурни ли сте, че искате да изтриете това поле? Това действие не може да бъде отменено.';

            this.customField_showModal();
        },

        customField_handleSort(event) {
            const sortField = event.currentTarget.dataset.sort;
            const url = new URL(window.location);

            let newOrder = 'ASC';
            if (url.searchParams.get('sort') === sortField && url.searchParams.get('order') === 'ASC') {
                newOrder = 'DESC';
            }

            url.searchParams.set('sort', sortField);
            url.searchParams.set('order', newOrder);
            url.searchParams.delete('page');

            window.location.href = url.toString();
        },

        customField_performDelete(fieldIds) {
            const formData = new FormData();
            fieldIds.forEach(id => {
                formData.append('selected[]', id);
            });
            formData.append('user_token', this.config.userToken);

            fetch(this.config.deleteUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => this.customField_handleDeleteResponse(data))
            .catch(error => this.customField_handleDeleteError(error));
        },

        customField_handleDeleteResponse(response) {
            if (response.success) {
                this.customField_showNotification(response.success, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else if (response.error) {
                this.customField_showNotification(response.error, 'error');
            }
        },

        customField_handleDeleteError(error) {
            this.customField_logDebug('Delete error:', error);
            this.customField_showNotification('Възникна грешка при изтриването', 'error');
        },

        customField_handleFieldTypeChange(event) {
            this.state.currentFieldType = event.target.value;
            this.customField_updateFieldValuesVisibility();
        },

        customField_removeFieldValue(event) {
            const valueItem = event.target.closest('.field-value-item');
            if (valueItem) {
                valueItem.remove();

                // Show "no values" message if no values left
                const remainingValues = this.elements.fieldValuesContainer?.querySelectorAll('.field-value-item');
                if (!remainingValues || remainingValues.length === 0) {
                    this.customField_showNoValuesMessage();
                }
            }
        },

        customField_updateBulkActionsVisibility() {
            if (this.elements.bulkActions) {
                if (this.state.selectedFields.size > 0) {
                    this.elements.bulkActions.classList.remove('hidden');
                } else {
                    this.elements.bulkActions.classList.add('hidden');
                }
            }
        },

        customField_handleBulkDelete() {
            if (this.state.selectedFields.size === 0) {
                this.customField_showNotification('Моля изберете полета за изтриване', 'warning');
                return;
            }

            const count = this.state.selectedFields.size;
            this.elements.deleteMessage.textContent = 
                `Сигурни ли сте, че искате да изтриете ${count} избрани полета? Това действие не може да бъде отменено.`;
            
            this.customField_showModal();
        },

        customField_handleSingleDelete(event) {
            const fieldId = event.target.closest('[data-custom-field-id]').dataset.customFieldId;
            this.state.selectedFields.clear();
            this.state.selectedFields.add(fieldId);
            
            this.elements.deleteMessage.textContent = 
                'Сигурни ли сте, че искате да изтриете това поле? Това действие не може да бъде отменено.';
            
            this.customField_showModal();
        },

        customField_confirmDelete() {
            if (this.state.selectedFields.size === 0) return;

            this.customField_hideModal();
            this.customField_performDelete(Array.from(this.state.selectedFields));
        },

        customField_performDelete(fieldIds) {
            const formData = new FormData();
            fieldIds.forEach(id => {
                formData.append('selected[]', id);
            });
            formData.append('user_token', this.config.userToken);

            fetch(this.config.deleteUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => this.customField_handleDeleteResponse(data))
            .catch(error => this.customField_handleDeleteError(error));
        },

        customField_handleDeleteResponse(response) {
            if (response.success) {
                this.customField_showNotification(response.success, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else if (response.error) {
                this.customField_showNotification(response.error, 'error');
            }
        },

        customField_handleDeleteError(error) {
            this.customField_logDebug('Delete error:', error);
            this.customField_showNotification('Възникна грешка при изтриването', 'error');
        },

        customField_handleSort(event) {
            const sortField = event.currentTarget.dataset.sort;
            const url = new URL(window.location);
            
            let newOrder = 'ASC';
            if (url.searchParams.get('sort') === sortField && url.searchParams.get('order') === 'ASC') {
                newOrder = 'DESC';
            }
            
            url.searchParams.set('sort', sortField);
            url.searchParams.set('order', newOrder);
            url.searchParams.delete('page');
            
            window.location.href = url.toString();
        },

        customField_handleFieldTypeChange(event) {
            this.state.currentFieldType = event.target.value;
            this.customField_updateFieldValuesVisibility();
        },

        customField_updateFieldValuesVisibility() {
            if (this.elements.fieldValuesSection) {
                const needsValues = ['select', 'radio', 'checkbox'].includes(this.state.currentFieldType);
                
                if (needsValues) {
                    this.elements.fieldValuesSection.classList.remove('hidden');
                } else {
                    this.elements.fieldValuesSection.classList.add('hidden');
                }
            }
        },

        customField_addFieldValue() {
            const valueHtml = this.customField_generateFieldValueHtml(this.state.fieldValueIndex);
            
            if (this.elements.fieldValuesContainer) {
                // Remove "no values" message if exists
                const noValuesMsg = this.elements.fieldValuesContainer.querySelector('.text-center');
                if (noValuesMsg) {
                    noValuesMsg.remove();
                }
                
                this.elements.fieldValuesContainer.insertAdjacentHTML('beforeend', valueHtml);
                this.state.fieldValueIndex++;
            }
        },

        customField_removeFieldValue(event) {
            const valueItem = event.target.closest('.field-value-item');
            if (valueItem) {
                valueItem.remove();
                
                // Show "no values" message if no values left
                const remainingValues = this.elements.fieldValuesContainer?.querySelectorAll('.field-value-item');
                if (!remainingValues || remainingValues.length === 0) {
                    this.customField_showNoValuesMessage();
                }
            }
        },

        customField_generateFieldValueHtml(index) {
            let languageInputs = '';

            if (this.config.languages && this.config.languages.length > 0) {
                this.config.languages.forEach(language => {
                    languageInputs += `
                        <input type="text" name="custom_field_value[${index}][custom_field_value_description][${language.language_id}][name]"
                               placeholder="Име на стойността (${language.name})"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    `;
                });
            } else {
                languageInputs = `
                    <input type="text" name="custom_field_value[${index}][custom_field_value_description][1][name]"
                           placeholder="Име на стойността"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md">
                `;
            }

            return `
                <div class="field-value-item bg-gray-50 rounded-lg p-4 border mb-4" data-value-index="${index}">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-900">Стойност ${index + 1}</h4>
                        <button type="button" class="remove-field-value text-red-600 hover:text-red-800">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                    <div class="grid grid-cols-1 gap-4">
                        ${languageInputs}
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Ред на сортиране</label>
                        <input type="number" name="custom_field_value[${index}][sort_order]" value="${index + 1}" min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                </div>
            `;
        },

        customField_showNoValuesMessage() {
            const messageHtml = `
                <div class="text-center py-8 text-gray-500">
                    <i class="ri-list-check text-4xl mb-4"></i>
                    <p>Няма добавени стойности</p>
                </div>
            `;
            this.elements.fieldValuesContainer.innerHTML = messageHtml;
        },

        customField_switchLanguageTab(event) {
            event.preventDefault();
            const langId = event.target.dataset.langTab;

            // Update tab buttons
            this.elements.langTabs.forEach(tab => {
                tab.classList.remove('active', 'border-primary', 'text-primary');
                tab.classList.add('border-transparent', 'text-gray-500');
            });

            event.target.classList.add('active', 'border-primary', 'text-primary');
            event.target.classList.remove('border-transparent', 'text-gray-500');

            // Update tab contents
            this.elements.langContents.forEach(content => {
                content.classList.add('hidden');
            });

            const targetContent = document.getElementById('lang-content-' + langId);
            if (targetContent) {
                targetContent.classList.remove('hidden');
            }
        },

        customField_showModal() {
            if (this.elements.deleteModal) {
                this.elements.deleteModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        },

        customField_hideModal() {
            if (this.elements.deleteModal) {
                this.elements.deleteModal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        },

        customField_showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        customField_logDebug(...args) {
            if (window.console && window.console.log) {
                console.log('[CustomFieldModule]', ...args);
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => CustomFieldModule.init());
    } else {
        CustomFieldModule.init();
    }

    // Expose module globally
    window.CustomFieldModule = CustomFieldModule;

})();
