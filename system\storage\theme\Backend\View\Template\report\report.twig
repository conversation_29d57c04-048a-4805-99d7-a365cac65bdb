<!-- Reports Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div class="flex items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">{{ heading_title }}</h1>
                <p class="text-sm text-gray-600 mt-1">Преглед и анализ на данните от магазина</p>
            </div>
        </div>
        <div class="flex items-center space-x-3 mt-4 md:mt-0">
            <button id="refresh-report" type="button" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors">
                <i class="ri-refresh-line mr-2"></i>
                Обнови
            </button>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <div class="">

        <!-- Report Selection Section -->
        <div class="bg-white rounded-lg shadow-sm mb-6 p-6">
            <div class="flex items-center mb-4">
                <i class="ri-bar-chart-line text-2xl text-primary mr-3"></i>
                <h2 class="text-lg font-semibold text-gray-800">Избор на отчет</h2>
            </div>

            {% if reports %}
            <div class="relative md:w-1/3" style="min-width: 300px;">
                <label for="report-select" class="block text-sm font-medium text-gray-700 mb-2">Изберете отчет за преглед</label>
                <div class="relative">
                    <select id="report-select" name="report" onchange="location = this.value;"
                            class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm bg-white appearance-none">
                        {% for report in reports %}
                            {% if code == report.code %}
                                <option value="{{ report.href }}" selected="selected">{{ report.text }}</option>
                            {% else %}
                                <option value="{{ report.href }}">{{ report.text }}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <i class="ri-arrow-down-s-line text-gray-400"></i>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-8">
                <i class="ri-bar-chart-line text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-600 text-lg">Няма налични отчети</p>
                <p class="text-gray-500 text-sm">Моля, инсталирайте и активирайте отчети от секцията Extensions</p>
            </div>
            {% endif %}
        </div>

        <!-- Report Content Section -->
        {% if report %}
        <div class="bg-white rounded-lg shadow-sm">
            <div class="border-b border-gray-200 px-6 py-4">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="ri-file-chart-line mr-2 text-primary"></i>
                    Резултати от отчета
                </h3>
            </div>
            <div class="p-6">
                <div id="report-content" class="report-content">
                    {{ report|raw }}
                </div>
            </div>
        </div>
        {% else %}
        <div class="bg-white rounded-lg shadow-sm p-8 text-center">
            <i class="ri-file-chart-line text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-semibold text-gray-600 mb-2">Изберете отчет</h3>
            <p class="text-gray-500">Моля, изберете отчет от падащото меню по-горе за да видите данните</p>
        </div>
        {% endif %}

        <!-- Report Statistics (if available) -->
        {% if reports %}
        <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                        <i class="ri-file-list-line text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Налични отчети</p>
                        <p class="text-2xl font-bold text-gray-900">{{ reports|length }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                        <i class="ri-eye-line text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Текущ отчет</p>
                        <p class="text-lg font-semibold text-gray-900">
                            {% if code %}
                                {% for report in reports %}
                                    {% if report.code == code %}
                                        {{ report.text }}
                                    {% endif %}
                                {% endfor %}
                            {% else %}
                                Няма избран
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                        <i class="ri-time-line text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">Последно обновяване</p>
                        <p class="text-sm text-gray-900">{{ "now"|date("d.m.Y H:i") }}</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</main>