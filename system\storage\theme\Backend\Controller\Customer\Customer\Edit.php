<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Edit extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('customers.log');
        // Зареждане на JavaScript файлове специфични за този контролер
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        $this->addBackendScriptWithVersion([
            'customer-form.js',
        ]);
    }

    /**
     * Изпълнява подготовката на данните за формата за клиент
     */
    public function execute() {
        $customer_id = (int)$this->requestGet('customer_id', 0);
        
        if ($customer_id) {
            $this->setTitle('Редактиране на клиент');
        } else {
            $this->setTitle('Добавяне на клиент');
        }
        
        $this->initAdminData();
        $this->prepareCustomerForm($customer_id);
        $this->renderTemplateWithDataAndOutput('customer/customer_form');
    }

    /**
     * Подготвя формата за клиент
     */
    public function prepareCustomerForm($customer_id = 0) {
        $this->prepareCustomerData($customer_id)
             ->prepareCustomerGroups()
             ->prepareStores()
             ->prepareCustomFields()
             ->prepareAddresses($customer_id)
             ->prepareHistoryData($customer_id)
             ->prepareTransactionData($customer_id)
             ->prepareRewardData($customer_id)
             ->prepareIpData($customer_id)
             ->prepareFormUrls($customer_id)
             ->prepareAdditionalData();

        return $this;
    }

    /**
     * Подготвя данните за клиента
     */
    private function prepareCustomerData($customer_id) {
        $this->loadModelAs('customer/customer', 'customerModel');

        if ($customer_id && ($customer_info = $this->customerModel->getCustomer($customer_id))) {
            $this->setData([
                'customer_id' => $customer_info['customer_id'],
                'customer_group_id' => $customer_info['customer_group_id'],
                'firstname' => $customer_info['firstname'],
                'lastname' => $customer_info['lastname'],
                'email' => $customer_info['email'],
                'telephone' => $customer_info['telephone'],
                'fax' => $customer_info['fax'],
                'newsletter' => $customer_info['newsletter'],
                'status' => $customer_info['status'],
                'approved' => $customer_info['approved'],
                'safe' => $customer_info['safe'],
                'date_added' => $customer_info['date_added']
            ]);
        } else {
            $this->setData([
                'customer_id' => 0,
                'customer_group_id' => $this->getConfig('config_customer_group_id'),
                'firstname' => '',
                'lastname' => '',
                'email' => '',
                'telephone' => '',
                'fax' => '',
                'newsletter' => 0,
                'status' => 1,
                'approved' => 1,
                'safe' => 0,
                'date_added' => ''
            ]);
        }

        return $this;
    }

    /**
     * Подготвя клиентските групи
     */
    private function prepareCustomerGroups() {
        $this->loadModelAs('customer/customer_group', 'customerGroupModel');
        $customer_groups = $this->customerGroupModel->getCustomerGroups();

        $this->setData('customer_groups', $customer_groups);

        return $this;
    }

    /**
     * Подготвя магазините
     */
    private function prepareStores() {
        $this->loadModelAs('setting/store', 'storeModel');
        $stores = $this->storeModel->getStores();

        $store_data = [];
        $store_data[] = [
            'store_id' => 0,
            'name' => $this->getConfig('config_name')
        ];

        foreach ($stores as $store) {
            $store_data[] = [
                'store_id' => $store['store_id'],
                'name' => $store['name']
            ];
        }

        $this->setData('stores', $store_data);

        return $this;
    }

    /**
     * Подготвя персонализираните полета
     */
    private function prepareCustomFields() {
        $this->loadModelAs('customer/custom_field', 'customFieldModel');
        $custom_fields = $this->customFieldModel->getCustomFields(['filter_customer' => 1]);

        $custom_field_data = [];
        foreach ($custom_fields as $custom_field) {
            $custom_field_data[] = [
                'custom_field_id' => $custom_field['custom_field_id'],
                'name' => $custom_field['name'],
                'type' => $custom_field['type'],
                'value' => $custom_field['value'],
                'validation' => $custom_field['validation'],
                'location' => $custom_field['location'],
                'required' => $custom_field['required'],
                'sort_order' => $custom_field['sort_order']
            ];
        }

        $this->setData('custom_fields', $custom_field_data);

        return $this;
    }

    /**
     * Подготвя адресите на клиента
     */
    private function prepareAddresses($customer_id) {
        if ($customer_id) {
            $this->loadModelAs('customer/customer', 'customerModel');
            $addresses = $this->customerModel->getAddresses($customer_id);

            $address_data = [];
            foreach ($addresses as $address) {
                $this->loadModelAs('localisation/country', 'countryModel');
                $country_info = $this->countryModel->getCountry($address['country_id']);

                if ($country_info) {
                    $this->loadModelAs('localisation/zone', 'zoneModel');
                    $zones = $this->zoneModel->getZonesByCountryId($address['country_id']);
                } else {
                    $zones = [];
                }

                $address_data[] = [
                    'address_id' => $address['address_id'],
                    'firstname' => $address['firstname'],
                    'lastname' => $address['lastname'],
                    'company' => $address['company'],
                    'address_1' => $address['address_1'],
                    'address_2' => $address['address_2'],
                    'city' => $address['city'],
                    'postcode' => $address['postcode'],
                    'country_id' => $address['country_id'],
                    'zone_id' => $address['zone_id'],
                    'zones' => $zones,
                    'default' => $address['default']
                ];
            }

            $this->setData('addresses', $address_data);
        } else {
            $this->setData('addresses', []);
        }

        return $this;
    }

    /**
     * Подготвя URL адресите за формата
     */
    private function prepareFormUrls($customer_id) {
        $this->setData([
            'action' => $this->getAdminLink('customer/customer/save'),
            'cancel' => $this->getAdminLink('customer/customer'),
            'login' => $customer_id ? $this->getAdminLink('customer/customer/login', 'customer_id=' . $customer_id) : '',
            'unlock' => $customer_id ? $this->getAdminLink('customer/customer/unlock', 'customer_id=' . $customer_id) : ''
        ]);

        return $this;
    }

    /**
     * Подготвя данни за History таба
     */
    private function prepareHistoryData($customer_id) {
        if ($customer_id) {
            $this->setData([
                'customer_id' => $customer_id,
                'history_url' => $this->getAdminLink('customer/customer/history', 'customer_id=' . $customer_id),
                'add_history_url' => $this->getAdminLink('customer/customer/addhistory')
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данни за Transaction таба
     */
    private function prepareTransactionData($customer_id) {
        if ($customer_id) {
            $this->setData([
                'transaction_url' => $this->getAdminLink('customer/customer/transaction', 'customer_id=' . $customer_id),
                'add_transaction_url' => $this->getAdminLink('customer/customer/addtransaction')
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данни за Reward таба
     */
    private function prepareRewardData($customer_id) {
        if ($customer_id) {
            $this->setData([
                'reward_url' => $this->getAdminLink('customer/customer/reward', 'customer_id=' . $customer_id),
                'add_reward_url' => $this->getAdminLink('customer/customer/addreward')
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данни за IP таба
     */
    private function prepareIpData($customer_id) {
        if ($customer_id) {
            $this->setData([
                'ip_url' => $this->getAdminLink('customer/customer/ip', 'customer_id=' . $customer_id)
            ]);
        }

        return $this;
    }

    /**
     * Подготвя допълнителни данни
     */
    private function prepareAdditionalData() {
        // Зареждане на страните
        $this->loadModelAs('localisation/country', 'countryModel');
        $countries = $this->countryModel->getCountries();

        // Зареждане на езиците
        $this->loadModelAs('localisation/language', 'languageModel');
        $languages = $this->languageModel->getLanguages();

        $this->setData([
            'countries' => $countries,
            'languages' => $languages,
            'user_token' => $this->session->data['user_token'],
            'back_url' => $this->getAdminLink('customer/customer'),
            'text_account' => 'Акаунт',
            'text_history' => 'История',
            'text_transaction' => 'Транзакции',
            'text_reward' => 'Reward Points',
            'text_ip' => 'IP адреси',
            'tab_general' => 'Общи',
            'tab_history' => 'История',
            'tab_transaction' => 'Транзакции',
            'tab_reward' => 'Reward Points',
            'tab_ip' => 'IP адреси',
            'tab_address' => 'Адрес'
        ]);

        return $this;
    }
}
