<!-- Customer Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-800">
				{% if customer_id %}Редактиране на клиент{% else %}Добавяне на клиент{% endif %}
			</h1>
			<p class="text-gray-500 mt-1">
				{% if customer_id %}Редактирайте данните на клиента{% else %}Въведете данните за новия клиент{% endif %}
			</p>
		</div>
		<div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
			<a href="{{ cancel }}" class="px-4 py-2 bg-gray-500 text-white rounded-button hover:bg-gray-600 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-arrow-left-line"></i>
				</div>
				<span>Назад</span>
			</a>
			<button type="submit" form="customer-form" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-save-line"></i>
				</div>
				<span>Запази</span>
			</button>
		</div>
	</div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<form id="customer-form" method="post" action="{{ action }}" class="space-y-6">
		<input type="hidden" name="customer_id" value="{{ customer_id }}">
		<input type="hidden" name="user_token" value="{{ user_token }}">

		<div class="max-w-7xl">
			<div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
				<div class="border-b border-gray-200">
					<div class="flex overflow-x-auto">
						<button type="button" data-tab="tab-general" class="tab-button active px-6 py-4 text-sm font-medium whitespace-nowrap border-b-2 border-primary text-primary">
							Основна информация
						</button>
						<button type="button" data-tab="tab-addresses" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap border-b-2 border-transparent hover:text-gray-700">
							Адреси
						</button>
						{% if custom_fields %}
						<button type="button" data-tab="tab-custom-fields" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap border-b-2 border-transparent hover:text-gray-700">
							Допълнителни полета
						</button>
						{% endif %}
					</div>
				</div>

				<!-- Tab Content -->
				<div class="p-6">
					<!-- General Tab -->
					<div id="tab-general" class="tab-content">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<!-- First Name -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Първо име <span class="text-red-500">*</span>
								</label>
								<input type="text" name="firstname" value="{{ firstname }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете първо име">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="firstname"></div>
							</div>

							<!-- Last Name -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Фамилия <span class="text-red-500">*</span>
								</label>
								<input type="text" name="lastname" value="{{ lastname }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете фамилия">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="lastname"></div>
							</div>

							<!-- Email -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Email адрес <span class="text-red-500">*</span>
								</label>
								<input type="email" name="email" value="{{ email }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете email адрес">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="email"></div>
							</div>

							<!-- Telephone -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Телефон <span class="text-red-500">*</span>
								</label>
								<input type="tel" name="telephone" value="{{ telephone }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете телефонен номер">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="telephone"></div>
							</div>

							<!-- Fax -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Факс</label>
								<input type="tel" name="fax" value="{{ fax }}"
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете факс номер">
							</div>

							<!-- Customer Group -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Клиентска група <span class="text-red-500">*</span>
								</label>
								<select name="customer_group_id" required
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
									{% for customer_group in customer_groups %}
										<option value="{{ customer_group.customer_group_id }}" {% if customer_group.customer_group_id == customer_group_id %}selected{% endif %}>
											{{ customer_group.name }}
										</option>
									{% endfor %}
								</select>
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="customer_group_id"></div>
							</div>
						</div>

						<!-- Additional Options -->
						<div class="mt-6 pt-6 border-t border-gray-200">
							<h3 class="text-lg font-medium text-gray-900 mb-4">Настройки</h3>
							<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
								<!-- Newsletter -->
								<div class="flex items-center">
									<input type="checkbox" name="newsletter" value="1" {% if newsletter %}checked{% endif %}
										   class="rounded border-gray-300 text-primary focus:ring-primary">
									<label class="ml-2 text-sm text-gray-700">Абониране за бюлетин</label>
								</div>

								<!-- Status -->
								<div class="flex items-center">
									<input type="checkbox" name="status" value="1" {% if status %}checked{% endif %}
										   class="rounded border-gray-300 text-primary focus:ring-primary">
									<label class="ml-2 text-sm text-gray-700">Активен</label>
								</div>

								<!-- Approved -->
								<div class="flex items-center">
									<input type="checkbox" name="approved" value="1" {% if approved %}checked{% endif %}
										   class="rounded border-gray-300 text-primary focus:ring-primary">
									<label class="ml-2 text-sm text-gray-700">Одобрен</label>
								</div>

								<!-- Safe -->
								<div class="flex items-center">
									<input type="checkbox" name="safe" value="1" {% if safe %}checked{% endif %}
										   class="rounded border-gray-300 text-primary focus:ring-primary">
									<label class="ml-2 text-sm text-gray-700">Безопасен (анти-фрод)</label>
								</div>
							</div>
						</div>

						{% if customer_id %}
						<!-- Customer Info -->
						<div class="mt-6 pt-6 border-t border-gray-200">
							<h3 class="text-lg font-medium text-gray-900 mb-4">Информация за клиента</h3>
							<div class="bg-gray-50 rounded-lg p-4">
								<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
									<div>
										<span class="font-medium text-gray-700">Дата на регистрация:</span>
										<span class="text-gray-900">{{ date_added }}</span>
									</div>
									<div>
										<span class="font-medium text-gray-700">ID на клиента:</span>
										<span class="text-gray-900">#{{ customer_id }}</span>
									</div>
								</div>
							</div>
						</div>

						<!-- Quick Actions -->
						<div class="mt-6 pt-6 border-t border-gray-200">
							<h3 class="text-lg font-medium text-gray-900 mb-4">Бързи действия</h3>
							<div class="flex flex-wrap gap-3">
								{% if login %}
								<a href="{{ login }}" target="_blank" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
									<i class="ri-login-box-line mr-2"></i>Влез като клиент
								</a>
								{% endif %}
								{% if unlock %}
								<button type="button" id="unlock-customer" data-url="{{ unlock }}" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors">
									<i class="ri-lock-unlock-line mr-2"></i>Отключи клиент
								</button>
								{% endif %}
							</div>
						</div>
						{% endif %}
					</div>

					<!-- Addresses Tab -->
					<div id="tab-addresses" class="tab-content hidden">
						<div class="space-y-6">
							<div class="flex items-center justify-between">
								<h3 class="text-lg font-medium text-gray-900">Адреси на клиента</h3>
								<button type="button" id="add-address" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
									<i class="ri-add-line mr-2"></i>Добави адрес
								</button>
							</div>

							<div id="addresses-container">
								{% if addresses %}
									{% for address in addresses %}
									<div class="address-item bg-gray-50 rounded-lg p-4 border" data-address-index="{{ loop.index0 }}">
										<div class="flex items-center justify-between mb-4">
											<h4 class="font-medium text-gray-900">Адрес {{ loop.index }}</h4>
											<button type="button" class="remove-address text-red-600 hover:text-red-800">
												<i class="ri-delete-bin-line"></i>
											</button>
										</div>
										<!-- Address fields would go here -->
										<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
											<input type="text" name="address[{{ loop.index0 }}][firstname]" value="{{ address.firstname }}" placeholder="Първо име" class="w-full px-3 py-2 border border-gray-300 rounded-md">
											<input type="text" name="address[{{ loop.index0 }}][lastname]" value="{{ address.lastname }}" placeholder="Фамилия" class="w-full px-3 py-2 border border-gray-300 rounded-md">
											<input type="text" name="address[{{ loop.index0 }}][company]" value="{{ address.company }}" placeholder="Компания" class="w-full px-3 py-2 border border-gray-300 rounded-md">
											<input type="text" name="address[{{ loop.index0 }}][address_1]" value="{{ address.address_1 }}" placeholder="Адрес 1" class="w-full px-3 py-2 border border-gray-300 rounded-md">
											<input type="text" name="address[{{ loop.index0 }}][address_2]" value="{{ address.address_2 }}" placeholder="Адрес 2" class="w-full px-3 py-2 border border-gray-300 rounded-md">
											<input type="text" name="address[{{ loop.index0 }}][city]" value="{{ address.city }}" placeholder="Град" class="w-full px-3 py-2 border border-gray-300 rounded-md">
											<input type="text" name="address[{{ loop.index0 }}][postcode]" value="{{ address.postcode }}" placeholder="Пощенски код" class="w-full px-3 py-2 border border-gray-300 rounded-md">
										</div>
										<div class="mt-4">
											<label class="flex items-center">
												<input type="checkbox" name="address[{{ loop.index0 }}][default]" value="1" {% if address.default %}checked{% endif %} class="rounded border-gray-300 text-primary focus:ring-primary">
												<span class="ml-2 text-sm text-gray-700">Адрес по подразбиране</span>
											</label>
										</div>
									</div>
									{% endfor %}
								{% else %}
									<div class="text-center py-8 text-gray-500">
										<i class="ri-map-pin-line text-4xl mb-4"></i>
										<p>Няма добавени адреси</p>
									</div>
								{% endif %}
							</div>
						</div>
					</div>

					<!-- Custom Fields Tab -->
					{% if custom_fields %}
					<div id="tab-custom-fields" class="tab-content hidden">
						<div class="space-y-6">
							<h3 class="text-lg font-medium text-gray-900">Допълнителни полета</h3>
							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								{% for custom_field in custom_fields %}
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">
										{{ custom_field.name }}
										{% if custom_field.required %}<span class="text-red-500">*</span>{% endif %}
									</label>
									{% if custom_field.type == 'text' %}
										<input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{{ custom_field.value }}" {% if custom_field.required %}required{% endif %}
											   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
									{% elseif custom_field.type == 'textarea' %}
										<textarea name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" {% if custom_field.required %}required{% endif %}
												  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" rows="3">{{ custom_field.value }}</textarea>
									{% elseif custom_field.type == 'select' %}
										<select name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" {% if custom_field.required %}required{% endif %}
												class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
											<option value="">Изберете...</option>
											<!-- Custom field options would be populated here -->
										</select>
									{% endif %}
								</div>
								{% endfor %}
							</div>
						</div>
					</div>
					{% endif %}
				</div>
			</div>
		</div>
	</form>
</main>

<script>
// Конфигурация за страницата
window.customerFormConfig = {
	userToken: '{{ user_token }}',
	customerId: {{ customer_id|default(0) }},
	countries: {{ countries|json_encode|raw }},
	unlockUrl: '{{ unlock|default("") }}'
};
</script>
