<!-- Customer Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-800">
				{% if customer_id %}Редактиране на клиент{% else %}Добавяне на клиент{% endif %}
			</h1>
			<p class="text-gray-500 mt-1">
				{% if customer_id %}Редактирайте данните на клиента{% else %}Въведете данните за новия клиент{% endif %}
			</p>
		</div>
		<div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
			<a href="{{ cancel }}" class="px-4 py-2 bg-gray-500 text-white rounded-button hover:bg-gray-600 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-arrow-left-line"></i>
				</div>
				<span>Назад</span>
			</a>
			<button type="submit" form="customer-form" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-save-line"></i>
				</div>
				<span>Запази</span>
			</button>
		</div>
	</div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<form id="customer-form" method="post" action="{{ action }}" class="space-y-6">
		<input type="hidden" name="customer_id" value="{{ customer_id }}">
		<input type="hidden" name="user_token" value="{{ user_token }}">

		<div class="max-w-7xl">
			<div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
				<div class="border-b border-gray-200">
					<div class="flex overflow-x-auto">
						<button type="button" data-tab="tab-general" class="tab-button active px-6 py-4 text-sm font-medium whitespace-nowrap border-b-2 border-primary text-primary">
							{{ tab_general|default('Основна информация') }}
						</button>
						<button type="button" data-tab="tab-addresses" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap border-b-2 border-transparent hover:text-gray-700">
							{{ tab_address|default('Адреси') }}
						</button>
						{% if customer_id %}
						<button type="button" data-tab="tab-history" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap border-b-2 border-transparent hover:text-gray-700">
							{{ tab_history|default('История') }}
						</button>
						<button type="button" data-tab="tab-transaction" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap border-b-2 border-transparent hover:text-gray-700">
							{{ tab_transaction|default('Транзакции') }}
						</button>
						<button type="button" data-tab="tab-reward" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap border-b-2 border-transparent hover:text-gray-700">
							{{ tab_reward|default('Reward Points') }}
						</button>
						<button type="button" data-tab="tab-ip" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap border-b-2 border-transparent hover:text-gray-700">
							{{ tab_ip|default('IP адреси') }}
						</button>
						{% endif %}
						{% if custom_fields %}
						<button type="button" data-tab="tab-custom-fields" class="tab-button px-6 py-4 text-sm font-medium text-gray-500 whitespace-nowrap border-b-2 border-transparent hover:text-gray-700">
							Допълнителни полета
						</button>
						{% endif %}
					</div>
				</div>

				<!-- Tab Content -->
				<div class="p-6">
					<!-- General Tab -->
					<div id="tab-general" class="tab-content">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<!-- First Name -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Първо име <span class="text-red-500">*</span>
								</label>
								<input type="text" name="firstname" value="{{ firstname }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете първо име">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="firstname"></div>
							</div>

							<!-- Last Name -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Фамилия <span class="text-red-500">*</span>
								</label>
								<input type="text" name="lastname" value="{{ lastname }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете фамилия">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="lastname"></div>
							</div>

							<!-- Email -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Email адрес <span class="text-red-500">*</span>
								</label>
								<input type="email" name="email" value="{{ email }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете email адрес">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="email"></div>
							</div>

							<!-- Telephone -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Телефон <span class="text-red-500">*</span>
								</label>
								<input type="tel" name="telephone" value="{{ telephone }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете телефонен номер">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="telephone"></div>
							</div>

							<!-- Fax -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Факс</label>
								<input type="tel" name="fax" value="{{ fax }}"
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете факс номер">
							</div>

							<!-- Customer Group -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Клиентска група <span class="text-red-500">*</span>
								</label>
								<select name="customer_group_id" required
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
									{% for customer_group in customer_groups %}
										<option value="{{ customer_group.customer_group_id }}" {% if customer_group.customer_group_id == customer_group_id %}selected{% endif %}>
											{{ customer_group.name }}
										</option>
									{% endfor %}
								</select>
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="customer_group_id"></div>
							</div>
						</div>

						<!-- Additional Options -->
						<div class="mt-6 pt-6 border-t border-gray-200">
							<h3 class="text-lg font-medium text-gray-900 mb-4">Настройки</h3>
							<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
								<!-- Newsletter -->
								<div class="flex items-center">
									<input type="checkbox" name="newsletter" value="1" {% if newsletter %}checked{% endif %}
										   class="rounded border-gray-300 text-primary focus:ring-primary">
									<label class="ml-2 text-sm text-gray-700">Абониране за бюлетин</label>
								</div>

								<!-- Status -->
								<div class="flex items-center">
									<input type="checkbox" name="status" value="1" {% if status %}checked{% endif %}
										   class="rounded border-gray-300 text-primary focus:ring-primary">
									<label class="ml-2 text-sm text-gray-700">Активен</label>
								</div>

								<!-- Approved -->
								<div class="flex items-center">
									<input type="checkbox" name="approved" value="1" {% if approved %}checked{% endif %}
										   class="rounded border-gray-300 text-primary focus:ring-primary">
									<label class="ml-2 text-sm text-gray-700">Одобрен</label>
								</div>

								<!-- Safe -->
								<div class="flex items-center">
									<input type="checkbox" name="safe" value="1" {% if safe %}checked{% endif %}
										   class="rounded border-gray-300 text-primary focus:ring-primary">
									<label class="ml-2 text-sm text-gray-700">Безопасен (анти-фрод)</label>
								</div>
							</div>
						</div>

						<!-- Password Section -->
						<div class="mt-6 pt-6 border-t border-gray-200">
							<div class="flex items-center justify-between mb-4">
								<h3 class="text-lg font-medium text-gray-900">Парола</h3>
								<div class="flex items-center">
									<input type="checkbox" id="change-password" class="rounded border-gray-300 text-primary focus:ring-primary">
									<label for="change-password" class="ml-2 text-sm text-gray-700">Смени паролата</label>
								</div>
							</div>
							<div id="password-fields" class="grid grid-cols-1 md:grid-cols-2 gap-6" style="display: none;">
								<!-- Password -->
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">
										Нова парола <span class="text-red-500">*</span>
									</label>
									<input type="password" name="password" value="{{ password|default('') }}"
										   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
										   placeholder="Въведете нова парола" autocomplete="new-password">
									<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="password"></div>
								</div>

								<!-- Confirm Password -->
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">
										Потвърди паролата <span class="text-red-500">*</span>
									</label>
									<input type="password" name="confirm" value="{{ confirm|default('') }}"
										   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
										   placeholder="Потвърдете новата парола" autocomplete="new-password">
									<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="confirm"></div>
								</div>
							</div>
						</div>

						{% if customer_id %}
						<!-- Customer Info -->
						<div class="mt-6 pt-6 border-t border-gray-200">
							<h3 class="text-lg font-medium text-gray-900 mb-4">Информация за клиента</h3>
							<div class="bg-gray-50 rounded-lg p-4">
								<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
									<div>
										<span class="font-medium text-gray-700">Дата на регистрация:</span>
										<span class="text-gray-900">{{ date_added }}</span>
									</div>
									<div>
										<span class="font-medium text-gray-700">ID на клиента:</span>
										<span class="text-gray-900">#{{ customer_id }}</span>
									</div>
								</div>
							</div>
						</div>

						<!-- Quick Actions -->
						<div class="mt-6 pt-6 border-t border-gray-200">
							<h3 class="text-lg font-medium text-gray-900 mb-4">Бързи действия</h3>
							<div class="flex flex-wrap gap-3">
								{# {% if login %}
								<a href="{{ login }}" target="_blank" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
									<i class="ri-login-box-line mr-2"></i>Влез като клиент
								</a>
								{% endif %} #}
								{% if unlock %}
								<button type="button" id="unlock-customer" data-url="{{ unlock }}" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors">
									<i class="ri-lock-unlock-line mr-2"></i>Отключи клиент
								</button>
								{% endif %}
							</div>
						</div>
						{% endif %}
					</div>

					<!-- Addresses Tab -->
					<div id="tab-addresses" class="tab-content hidden">
						<div class="space-y-6">
							<div class="flex items-center justify-between">
								<h3 class="text-lg font-medium text-gray-900">Адреси на клиента</h3>
								<button type="button" id="add-address" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
									<i class="ri-add-line mr-2"></i>Добави адрес
								</button>
							</div>

							<div id="addresses-container">
								{% if addresses %}
									{% for address in addresses %}
									<div class="address-item bg-gray-50 rounded-lg p-4 border" data-address-index="{{ loop.index0 }}">
										<div class="flex items-center justify-between mb-4">
											<h4 class="font-medium text-gray-900">Адрес {{ loop.index }}</h4>
											<button type="button" class="remove-address text-red-600 hover:text-red-800">
												<i class="ri-delete-bin-line"></i>
											</button>
										</div>
										<!-- Address fields -->
										<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
											<!-- First Name -->
											<div>
												<label for="address_{{ loop.index0 }}_firstname" class="block text-sm font-medium text-gray-700 mb-1">
													Първо име <span class="text-red-500">*</span>
												</label>
												<input type="text" id="address_{{ loop.index0 }}_firstname" name="address[{{ loop.index0 }}][firstname]" value="{{ address.firstname }}" required
													   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
													   placeholder="Въведете първо име">
											</div>

											<!-- Last Name -->
											<div>
												<label for="address_{{ loop.index0 }}_lastname" class="block text-sm font-medium text-gray-700 mb-1">
													Фамилия <span class="text-red-500">*</span>
												</label>
												<input type="text" id="address_{{ loop.index0 }}_lastname" name="address[{{ loop.index0 }}][lastname]" value="{{ address.lastname }}" required
													   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
													   placeholder="Въведете фамилия">
											</div>

											<!-- Company -->
											<div>
												<label for="address_{{ loop.index0 }}_company" class="block text-sm font-medium text-gray-700 mb-1">Компания</label>
												<input type="text" id="address_{{ loop.index0 }}_company" name="address[{{ loop.index0 }}][company]" value="{{ address.company }}"
													   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
													   placeholder="Въведете име на компания">
												<p class="text-xs text-gray-500 mt-1">Незадължително поле</p>
											</div>

											<!-- Address 1 -->
											<div>
												<label for="address_{{ loop.index0 }}_address_1" class="block text-sm font-medium text-gray-700 mb-1">
													Адрес 1 <span class="text-red-500">*</span>
												</label>
												<input type="text" id="address_{{ loop.index0 }}_address_1" name="address[{{ loop.index0 }}][address_1]" value="{{ address.address_1 }}" required
													   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
													   placeholder="Въведете основен адрес">
											</div>

											<!-- Address 2 -->
											<div>
												<label for="address_{{ loop.index0 }}_address_2" class="block text-sm font-medium text-gray-700 mb-1">Адрес 2</label>
												<input type="text" id="address_{{ loop.index0 }}_address_2" name="address[{{ loop.index0 }}][address_2]" value="{{ address.address_2 }}"
													   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
													   placeholder="Допълнителен адрес (апартамент, етаж)">
												<p class="text-xs text-gray-500 mt-1">Незадължително поле</p>
											</div>

											<!-- City -->
											<div>
												<label for="address_{{ loop.index0 }}_city" class="block text-sm font-medium text-gray-700 mb-1">
													Град <span class="text-red-500">*</span>
												</label>
												<input type="text" id="address_{{ loop.index0 }}_city" name="address[{{ loop.index0 }}][city]" value="{{ address.city }}" required
													   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
													   placeholder="Въведете град">
											</div>

											<!-- Postcode -->
											<div>
												<label for="address_{{ loop.index0 }}_postcode" class="block text-sm font-medium text-gray-700 mb-1">
													Пощенски код <span class="text-red-500">*</span>
												</label>
												<input type="text" id="address_{{ loop.index0 }}_postcode" name="address[{{ loop.index0 }}][postcode]" value="{{ address.postcode }}" required
													   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
													   placeholder="Въведете пощенски код">
											</div>

											<!-- Country -->
											<div>
												<label for="address_{{ loop.index0 }}_country_id" class="block text-sm font-medium text-gray-700 mb-1">
													Държава <span class="text-red-500">*</span>
												</label>
												<select id="address_{{ loop.index0 }}_country_id" name="address[{{ loop.index0 }}][country_id]" required
														class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
													<option value="">Изберете държава</option>
													{% for country in countries %}
														<option value="{{ country.country_id }}" {% if country.country_id == address.country_id %}selected{% endif %}>{{ country.name }}</option>
													{% endfor %}
												</select>
											</div>

											<!-- Zone -->
											<div>
												<label for="address_{{ loop.index0 }}_zone_id" class="block text-sm font-medium text-gray-700 mb-1">
													Област/Регион <span class="text-red-500">*</span>
												</label>
												<select id="address_{{ loop.index0 }}_zone_id" name="address[{{ loop.index0 }}][zone_id]" required
														class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
													<option value="">Изберете област</option>
													{% for zone in zones %}
														<option value="{{ zone.zone_id }}" {% if zone.zone_id == address.zone_id %}selected{% endif %}>{{ zone.name }}</option>
													{% endfor %}
												</select>
											</div>
										</div>

										<!-- Default Address Option -->
										<div class="mt-4">
											<fieldset>
												<legend class="text-sm font-medium text-gray-700 mb-2">Настройки на адреса</legend>
												<label for="address_{{ loop.index0 }}_default" class="flex items-center">
													<input type="checkbox" id="address_{{ loop.index0 }}_default" name="address[{{ loop.index0 }}][default]" value="1" {% if address.default %}checked{% endif %}
														   class="rounded border-gray-300 text-primary focus:ring-primary">
													<span class="ml-2 text-sm text-gray-700">Адрес по подразбиране</span>
												</label>
												<p class="text-xs text-gray-500 mt-1">Този адрес ще се използва като основен за доставки</p>
											</fieldset>
										</div>
									</div>
									{% endfor %}
								{% else %}
									<div class="text-center py-8 text-gray-500">
										<i class="ri-map-pin-line text-4xl mb-4"></i>
										<p>Няма добавени адреси</p>
									</div>
								{% endif %}
							</div>
						</div>
					</div>

					<!-- Custom Fields Tab -->
					{% if custom_fields %}
					<div id="tab-custom-fields" class="tab-content hidden">
						<div class="space-y-6">
							<h3 class="text-lg font-medium text-gray-900">Допълнителни полета</h3>
							<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
								{% for custom_field in custom_fields %}
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">
										{{ custom_field.name }}
										{% if custom_field.required %}<span class="text-red-500">*</span>{% endif %}
									</label>
									{% if custom_field.type == 'text' %}
										<input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{{ custom_field.value }}" {% if custom_field.required %}required{% endif %}
											   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
									{% elseif custom_field.type == 'textarea' %}
										<textarea name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" {% if custom_field.required %}required{% endif %}
												  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" rows="3">{{ custom_field.value }}</textarea>
									{% elseif custom_field.type == 'select' %}
										<select name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" {% if custom_field.required %}required{% endif %}
												class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
											<option value="">Изберете...</option>
											<!-- Custom field options would be populated here -->
										</select>
									{% endif %}
								</div>
								{% endfor %}
							</div>
						</div>
					</div>
					{% endif %}

					{% if customer_id %}
					<!-- History Tab -->
					<div id="tab-history" class="tab-content hidden">
						<div class="p-6 space-y-6">
							<!-- History List -->
							<div class="bg-gray-50 rounded-lg p-4">
								<h3 class="text-lg font-medium text-gray-900 mb-4">{{ text_history|default('История на клиента') }}</h3>
								<div id="history-list" class="space-y-3">
									<!-- History items will be loaded here via AJAX -->
								</div>
							</div>

							<!-- Add History Form -->
							<div class="bg-white border border-gray-200 rounded-lg p-6">
								<h4 class="text-md font-medium text-gray-900 mb-4">Добавяне на коментар</h4>
								<div class="space-y-4">
									<div>
										<label for="history-comment" class="block text-sm font-medium text-gray-700 mb-2">
											Коментар
										</label>
										<textarea id="history-comment" name="history_comment" rows="4"
												  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
												  placeholder="Въведете коментар за историята на клиента..."></textarea>
									</div>
									<div class="flex justify-end">
										<button type="button" id="add-history-btn"
												class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center">
											<i class="ri-add-line mr-2"></i>
											Добави коментар
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Transaction Tab -->
					<div id="tab-transaction" class="tab-content hidden">
						<div class="p-6 space-y-6">
							<!-- Transaction List -->
							<div class="bg-gray-50 rounded-lg p-4">
								<h3 class="text-lg font-medium text-gray-900 mb-4">{{ text_transaction|default('Транзакции') }}</h3>
								<div id="transaction-list" class="space-y-3">
									<!-- Transaction items will be loaded here via AJAX -->
								</div>
							</div>

							<!-- Add Transaction Form -->
							<div class="bg-white border border-gray-200 rounded-lg p-6">
								<h4 class="text-md font-medium text-gray-900 mb-4">Добавяне на транзакция</h4>
								<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<label for="transaction-description" class="block text-sm font-medium text-gray-700 mb-2">
											Описание
										</label>
										<input type="text" id="transaction-description" name="transaction_description"
											   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
											   placeholder="Описание на транзакцията...">
									</div>
									<div>
										<label for="transaction-amount" class="block text-sm font-medium text-gray-700 mb-2">
											Сума
										</label>
										<input type="number" step="0.01" id="transaction-amount" name="transaction_amount"
											   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
											   placeholder="0.00">
									</div>
								</div>
								<div class="flex justify-end mt-4">
									<button type="button" id="add-transaction-btn"
											class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center">
										<i class="ri-add-line mr-2"></i>
										Добави транзакция
									</button>
								</div>
							</div>
						</div>
					</div>

					<!-- Reward Points Tab -->
					<div id="tab-reward" class="tab-content hidden">
						<div class="p-6 space-y-6">
							<!-- Reward Points List -->
							<div class="bg-gray-50 rounded-lg p-4">
								<h3 class="text-lg font-medium text-gray-900 mb-4">{{ text_reward|default('Reward Points') }}</h3>
								<div id="reward-list" class="space-y-3">
									<!-- Reward items will be loaded here via AJAX -->
								</div>
							</div>

							<!-- Add Reward Points Form -->
							<div class="bg-white border border-gray-200 rounded-lg p-6">
								<h4 class="text-md font-medium text-gray-900 mb-4">Добавяне на reward points</h4>
								<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<label for="reward-description" class="block text-sm font-medium text-gray-700 mb-2">
											Описание
										</label>
										<input type="text" id="reward-description" name="reward_description"
											   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
											   placeholder="Описание на reward points...">
									</div>
									<div>
										<label for="reward-points" class="block text-sm font-medium text-gray-700 mb-2">
											Точки
										</label>
										<input type="number" id="reward-points" name="reward_points"
											   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
											   placeholder="0">
									</div>
								</div>
								<div class="flex justify-end mt-4">
									<button type="button" id="add-reward-btn"
											class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center">
										<i class="ri-add-line mr-2"></i>
										Добави точки
									</button>
								</div>
							</div>
						</div>
					</div>

					<!-- IP Address Tab -->
					<div id="tab-ip" class="tab-content hidden">
						<div class="p-6">
							<div class="bg-gray-50 rounded-lg p-4">
								<h3 class="text-lg font-medium text-gray-900 mb-4">{{ text_ip|default('IP адреси') }}</h3>
								<div id="ip-list" class="space-y-3">
									<!-- IP addresses will be loaded here via AJAX -->
								</div>
							</div>
						</div>
					</div>
					{% endif %}
				</div>
			</div>
		</div>
	</form>
</main>

<script>
// Конфигурация за страницата
window.customerFormConfig = {
	userToken: '{{ user_token }}',
	customerId: {{ customer_id|default(0) }},
	countries: {{ countries|json_encode|raw }},
	unlockUrl: '{{ unlock|default("") }}',
	historyUrl: '{{ history_url|default("") }}',
	addHistoryUrl: '{{ add_history_url|default("") }}',
	transactionUrl: '{{ transaction_url|default("") }}',
	addTransactionUrl: '{{ add_transaction_url|default("") }}',
	rewardUrl: '{{ reward_url|default("") }}',
	addRewardUrl: '{{ add_reward_url|default("") }}',
	ipUrl: '{{ ip_url|default("") }}'
};
</script>
