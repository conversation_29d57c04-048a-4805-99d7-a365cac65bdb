<!-- Custom Field Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-800">
				{% if custom_field_id %}Редактиране на персонализирано поле{% else %}Добавяне на персонализирано поле{% endif %}
			</h1>
			<p class="text-gray-500 mt-1">
				{% if custom_field_id %}Редактирайте настройките на полето{% else %}Създайте ново персонализирано поле{% endif %}
			</p>
		</div>
		<div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
			<a href="{{ cancel }}" class="px-4 py-2 bg-gray-500 text-white rounded-button hover:bg-gray-600 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-arrow-left-line"></i>
				</div>
				<span>Назад</span>
			</a>
			<button type="submit" form="custom-field-form" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-save-line"></i>
				</div>
				<span>Запази</span>
			</button>
		</div>
	</div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<form id="custom-field-form" method="post" action="{{ action }}" class="space-y-6">
		<input type="hidden" name="custom_field_id" value="{{ custom_field_id }}">
		<input type="hidden" name="user_token" value="{{ user_token }}">

		<div class="max-w-4xl">
			<div class="bg-white rounded-lg shadow-sm overflow-hidden">
				<div class="p-6">
					<!-- Multi-language Names -->
					<div class="mb-8">
						<h3 class="text-lg font-medium text-gray-900 mb-4">Име на полето</h3>
						
						{% if languages|length > 1 %}
						<!-- Language Tabs -->
						<div class="border-b border-gray-200 mb-4">
							<nav class="-mb-px flex space-x-8">
								{% for language in languages %}
								<button type="button" data-lang-tab="{{ language.language_id }}" class="lang-tab {% if loop.first %}active{% endif %} py-2 px-1 border-b-2 font-medium text-sm {% if loop.first %}border-primary text-primary{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %}">
									<img src="{{ language.image }}" alt="{{ language.name }}" class="inline-block w-4 h-4 mr-2">
									{{ language.name }}
								</button>
								{% endfor %}
							</nav>
						</div>

						<!-- Language Content -->
						{% for language in languages %}
						<div id="lang-content-{{ language.language_id }}" class="lang-content {% if not loop.first %}hidden{% endif %}">
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Име на полето ({{ language.name }}) <span class="text-red-500">*</span>
								</label>
								<input type="text" name="custom_field_description[{{ language.language_id }}][name]" 
									   value="{{ custom_field_description[language.language_id].name|default('') }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете име на полето">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="name-{{ language.language_id }}"></div>
							</div>
						</div>
						{% endfor %}
						{% else %}
						<!-- Single Language -->
						{% set language = languages|first %}
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-1">
								Име на полето <span class="text-red-500">*</span>
							</label>
							<input type="text" name="custom_field_description[{{ language.language_id }}][name]" 
								   value="{{ custom_field_description[language.language_id].name|default('') }}" required
								   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
								   placeholder="Въведете име на полето">
							<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="name"></div>
						</div>
						{% endif %}
					</div>

					<!-- Field Settings -->
					<div class="pt-6 border-t border-gray-200">
						<h3 class="text-lg font-medium text-gray-900 mb-4">Настройки на полето</h3>
						
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<!-- Field Type -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Тип на полето <span class="text-red-500">*</span>
								</label>
								<select name="type" id="field-type" required
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
									<option value="text" {% if type == 'text' %}selected{% endif %}>Текст</option>
									<option value="textarea" {% if type == 'textarea' %}selected{% endif %}>Текстова област</option>
									<option value="select" {% if type == 'select' %}selected{% endif %}>Избор (Select)</option>
									<option value="radio" {% if type == 'radio' %}selected{% endif %}>Радио бутони</option>
									<option value="checkbox" {% if type == 'checkbox' %}selected{% endif %}>Отметки</option>
									<option value="file" {% if type == 'file' %}selected{% endif %}>Файл</option>
									<option value="date" {% if type == 'date' %}selected{% endif %}>Дата</option>
									<option value="time" {% if type == 'time' %}selected{% endif %}>Време</option>
									<option value="datetime" {% if type == 'datetime' %}selected{% endif %}>Дата и време</option>
								</select>
							</div>

							<!-- Field Location -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Местоположение <span class="text-red-500">*</span>
								</label>
								<select name="location" required
										class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
									<option value="account" {% if location == 'account' %}selected{% endif %}>Акаунт</option>
									<option value="address" {% if location == 'address' %}selected{% endif %}>Адрес</option>
								</select>
								<p class="text-xs text-gray-500 mt-1">
									Определя къде ще се показва полето в формата
								</p>
							</div>

							<!-- Sort Order -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Ред на сортиране
								</label>
								<input type="number" name="sort_order" value="{{ sort_order|default(1) }}" min="0"
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="0">
							</div>

							<!-- Status -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-3">Статус</label>
								<div class="space-y-2">
									<label class="flex items-center">
										<input type="radio" name="status" value="1" {% if status %}checked{% endif %}
											   class="text-primary focus:ring-primary border-gray-300">
										<span class="ml-2 text-sm text-gray-700">Активно</span>
									</label>
									<label class="flex items-center">
										<input type="radio" name="status" value="0" {% if not status %}checked{% endif %}
											   class="text-primary focus:ring-primary border-gray-300">
										<span class="ml-2 text-sm text-gray-700">Неактивно</span>
									</label>
								</div>
							</div>
						</div>

						<!-- Validation -->
						<div class="mt-6">
							<label class="block text-sm font-medium text-gray-700 mb-1">Валидация (Regex)</label>
							<input type="text" name="validation" value="{{ validation }}"
								   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
								   placeholder="Например: /^[0-9]+$/ за числа">
							<p class="text-xs text-gray-500 mt-1">
								Опционално: регулярен израз за валидация на въведените данни
							</p>
						</div>
					</div>

					<!-- Field Values (for select, radio, checkbox) -->
					<div id="field-values-section" class="mt-6 pt-6 border-t border-gray-200 {% if type not in ['select', 'radio', 'checkbox'] %}hidden{% endif %}">
						<div class="flex items-center justify-between mb-4">
							<h3 class="text-lg font-medium text-gray-900">Стойности на полето</h3>
							<button type="button" id="add-field-value" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
								<i class="ri-add-line mr-2"></i>Добави стойност
							</button>
						</div>

						<div id="field-values-container">
							{% if custom_field_value %}
								{% for value in custom_field_value %}
								<div class="field-value-item bg-gray-50 rounded-lg p-4 border mb-4" data-value-index="{{ loop.index0 }}">
									<div class="flex items-center justify-between mb-4">
										<h4 class="font-medium text-gray-900">Стойност {{ loop.index }}</h4>
										<button type="button" class="remove-field-value text-red-600 hover:text-red-800">
											<i class="ri-delete-bin-line"></i>
										</button>
									</div>
									<!-- Multi-language value names would go here -->
									<div class="grid grid-cols-1 gap-4">
										{% for language in languages %}
										<input type="text" name="custom_field_value[{{ loop.parent.loop.index0 }}][custom_field_value_description][{{ language.language_id }}][name]" 
											   value="{{ value.custom_field_value_description[language.language_id].name|default('') }}" 
											   placeholder="Име на стойността ({{ language.name }})" 
											   class="w-full px-3 py-2 border border-gray-300 rounded-md">
										{% endfor %}
									</div>
									<div class="mt-4">
										<label class="block text-sm font-medium text-gray-700 mb-1">Ред на сортиране</label>
										<input type="number" name="custom_field_value[{{ loop.index0 }}][sort_order]" value="{{ value.sort_order|default(loop.index) }}" min="0"
											   class="w-full px-3 py-2 border border-gray-300 rounded-md">
									</div>
								</div>
								{% endfor %}
							{% else %}
								<div class="text-center py-8 text-gray-500">
									<i class="ri-list-check text-4xl mb-4"></i>
									<p>Няма добавени стойности</p>
								</div>
							{% endif %}
						</div>
					</div>

					<!-- Customer Groups -->
					<div class="mt-6 pt-6 border-t border-gray-200">
						<h3 class="text-lg font-medium text-gray-900 mb-4">Клиентски групи</h3>
						<p class="text-sm text-gray-500 mb-4">Изберете за кои клиентски групи да се показва това поле</p>
						
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							{% for customer_group in customer_groups %}
							<label class="flex items-center">
								<input type="checkbox" name="custom_field_customer_group[]" value="{{ customer_group.customer_group_id }}" 
									   {% if customer_group.customer_group_id in custom_field_customer_group %}checked{% endif %}
									   class="rounded border-gray-300 text-primary focus:ring-primary">
								<span class="ml-2 text-sm text-gray-700">{{ customer_group.name }}</span>
							</label>
							{% endfor %}
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</main>

<script>
// Конфигурация за страницата
window.customFieldFormConfig = {
	userToken: '{{ user_token }}',
	customFieldId: {{ custom_field_id|default(0) }},
	languages: {{ languages|json_encode|raw }},
	customFieldValue: {{ custom_field_value|json_encode|raw }}
};
</script>
