/**
 * Customer Form Module
 * Handles customer form validation, tabs, address management, and AJAX operations
 */
(function() {
    'use strict';

    const CustomerFormModule = {
        config: {
            userToken: '',
            customerId: 0,
            countries: [],
            unlockUrl: '',
            historyUrl: '',
            addHistoryUrl: '',
            transactionUrl: '',
            addTransactionUrl: '',
            rewardUrl: '',
            addRewardUrl: '',
            ipUrl: '',
            maxConcurrentRequests: 10,
            activeRequests: 0
        },

        elements: {
            form: null,
            tabButtons: null,
            tabContents: null,
            addAddressBtn: null,
            addressesContainer: null,
            unlockBtn: null,
            errorMessages: null,
            // Password elements
            changePasswordCheckbox: null,
            passwordFields: null,
            passwordInput: null,
            confirmInput: null,
            // New tab elements
            historyList: null,
            historyComment: null,
            addHistoryBtn: null,
            transactionList: null,
            transactionDescription: null,
            transactionAmount: null,
            addTransactionBtn: null,
            rewardList: null,
            rewardDescription: null,
            rewardPoints: null,
            addRewardBtn: null,
            ipList: null
        },

        state: {
            currentTab: 'tab-general',
            addressIndex: 0,
            isSubmitting: false
        },

        init() {
            this.customerForm_loadConfig();
            this.customerForm_cacheElements();
            this.customerForm_bindEvents();
            this.customerForm_initializeTabs();
            this.customerForm_initializeAddresses();
            this.customerForm_logDebug('Customer form module initialized');
        },

        customerForm_loadConfig() {
            if (window.customerFormConfig) {
                Object.assign(this.config, window.customerFormConfig);
            }
        },

        customerForm_cacheElements() {
            this.elements.form = document.getElementById('customer-form');
            this.elements.tabButtons = document.querySelectorAll('.tab-button');
            this.elements.tabContents = document.querySelectorAll('.tab-content');
            this.elements.addAddressBtn = document.getElementById('add-address');
            this.elements.addressesContainer = document.getElementById('addresses-container');
            this.elements.unlockBtn = document.getElementById('unlock-customer');
            this.elements.errorMessages = document.querySelectorAll('.error-message');

            // Cache password elements
            this.elements.changePasswordCheckbox = document.getElementById('change-password');
            this.elements.passwordFields = document.getElementById('password-fields');
            this.elements.passwordInput = document.querySelector('input[name="password"]');
            this.elements.confirmInput = document.querySelector('input[name="confirm"]');

            // Cache new tab elements
            this.elements.historyList = document.getElementById('history-list');
            this.elements.historyComment = document.getElementById('history-comment');
            this.elements.addHistoryBtn = document.getElementById('add-history-btn');
            this.elements.transactionList = document.getElementById('transaction-list');
            this.elements.transactionDescription = document.getElementById('transaction-description');
            this.elements.transactionAmount = document.getElementById('transaction-amount');
            this.elements.addTransactionBtn = document.getElementById('add-transaction-btn');
            this.elements.rewardList = document.getElementById('reward-list');
            this.elements.rewardDescription = document.getElementById('reward-description');
            this.elements.rewardPoints = document.getElementById('reward-points');
            this.elements.addRewardBtn = document.getElementById('add-reward-btn');
            this.elements.ipList = document.getElementById('ip-list');
        },

        customerForm_bindEvents() {
            // Form submission
            if (this.elements.form) {
                this.elements.form.addEventListener('submit', (e) => this.customerForm_handleSubmit(e));
            }

            // Tab switching
            this.elements.tabButtons.forEach(button => {
                button.addEventListener('click', (e) => this.customerForm_switchTab(e));
            });

            // Add address
            if (this.elements.addAddressBtn) {
                this.elements.addAddressBtn.addEventListener('click', () => this.customerForm_addAddress());
            }

            // Remove address (delegated)
            if (this.elements.addressesContainer) {
                this.elements.addressesContainer.addEventListener('click', (e) => {
                    if (e.target.closest('.remove-address')) {
                        this.customerForm_removeAddress(e);
                    }
                });
            }

            // Unlock customer
            if (this.elements.unlockBtn) {
                this.elements.unlockBtn.addEventListener('click', () => this.customerForm_unlockCustomer());
            }

            // New tab event listeners
            if (this.elements.addHistoryBtn) {
                this.elements.addHistoryBtn.addEventListener('click', () => this.customerForm_addHistory());
            }

            if (this.elements.addTransactionBtn) {
                this.elements.addTransactionBtn.addEventListener('click', () => this.customerForm_addTransaction());
            }

            if (this.elements.addRewardBtn) {
                this.elements.addRewardBtn.addEventListener('click', () => this.customerForm_addReward());
            }

            // Password toggle functionality
            if (this.elements.changePasswordCheckbox) {
                this.elements.changePasswordCheckbox.addEventListener('change', (e) => this.customerForm_togglePasswordFields(e));
            }

            // Real-time validation
            this.customerForm_bindValidation();
        },

        customerForm_initializeTabs() {
            // Show first tab by default
            if (this.elements.tabContents.length > 0) {
                this.elements.tabContents[0].classList.remove('hidden');
            }
        },

        customerForm_initializeAddresses() {
            // Set initial address index based on existing addresses
            const existingAddresses = this.elements.addressesContainer?.querySelectorAll('.address-item');
            this.state.addressIndex = existingAddresses ? existingAddresses.length : 0;
        },

        customerForm_switchTab(event) {
            event.preventDefault();
            const targetTab = event.target.dataset.tab;

            // Update tab buttons
            this.elements.tabButtons.forEach(button => {
                button.classList.remove('active', 'border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            event.target.classList.add('active', 'border-primary', 'text-primary');
            event.target.classList.remove('border-transparent', 'text-gray-500');

            // Update tab contents
            this.elements.tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.remove('hidden');
                this.state.currentTab = targetTab;

                // Load data for specific tabs
                if (this.config.customerId > 0) {
                    switch (targetTab) {
                        case 'tab-history':
                            this.customerForm_loadHistory();
                            break;
                        case 'tab-transaction':
                            this.customerForm_loadTransactions();
                            break;
                        case 'tab-reward':
                            this.customerForm_loadRewards();
                            break;
                        case 'tab-ip':
                            this.customerForm_loadIpAddresses();
                            break;
                    }
                }
            }
        },

        customerForm_addAddress() {
            const addressHtml = this.customerForm_generateAddressHtml(this.state.addressIndex);

            if (this.elements.addressesContainer) {
                // Remove "no addresses" message if exists
                const noAddressesMsg = this.elements.addressesContainer.querySelector('.text-center');
                if (noAddressesMsg) {
                    noAddressesMsg.remove();
                }

                this.elements.addressesContainer.insertAdjacentHTML('beforeend', addressHtml);
                this.state.addressIndex++;
            }
        },

        customerForm_removeAddress(event) {
            const addressItem = event.target.closest('.address-item');
            if (addressItem) {
                addressItem.remove();

                // Show "no addresses" message if no addresses left
                const remainingAddresses = this.elements.addressesContainer?.querySelectorAll('.address-item');
                if (!remainingAddresses || remainingAddresses.length === 0) {
                    this.customerForm_showNoAddressesMessage();
                }
            }
        },

        customerForm_generateAddressHtml(index) {
            return `
                <div class="address-item bg-gray-50 rounded-lg p-4 border" data-address-index="${index}">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-900">Адрес ${index + 1}</h4>
                        <button type="button" class="remove-address text-red-600 hover:text-red-800">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input type="text" name="address[${index}][firstname]" placeholder="Първо име" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][lastname]" placeholder="Фамилия" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][company]" placeholder="Компания" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][address_1]" placeholder="Адрес 1" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][address_2]" placeholder="Адрес 2" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][city]" placeholder="Град" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][postcode]" placeholder="Пощенски код" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    <div class="mt-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="address[${index}][default]" value="1" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-sm text-gray-700">Адрес по подразбиране</span>
                        </label>
                    </div>
                </div>
            `;
        },



        customerForm_generateAddressHtml(index) {
            return `
                <div class="address-item bg-gray-50 rounded-lg p-4 border" data-address-index="${index}">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-900">Адрес ${index + 1}</h4>
                        <button type="button" class="remove-address text-red-600 hover:text-red-800">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input type="text" name="address[${index}][firstname]" placeholder="Първо име" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][lastname]" placeholder="Фамилия" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][company]" placeholder="Компания" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][address_1]" placeholder="Адрес 1" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][address_2]" placeholder="Адрес 2" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][city]" placeholder="Град" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][postcode]" placeholder="Пощенски код" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    <div class="mt-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="address[${index}][default]" value="1" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-sm text-gray-700">Адрес по подразбиране</span>
                        </label>
                    </div>
                </div>
            `;
        },

        customerForm_showNoAddressesMessage() {
            const messageHtml = `
                <div class="text-center py-8 text-gray-500">
                    <i class="ri-map-pin-line text-4xl mb-4"></i>
                    <p>Няма добавени адреси</p>
                </div>
            `;
            this.elements.addressesContainer.innerHTML = messageHtml;
        },

        customerForm_handleSubmit(event) {
            event.preventDefault();
            
            if (this.state.isSubmitting) {
                return;
            }
            
            this.customerForm_clearErrors();
            
            if (!this.customerForm_validateForm()) {
                this.customerForm_showNotification('Моля поправете грешките във формата', 'error');
                return;
            }
            
            this.state.isSubmitting = true;
            this.customerForm_submitForm();
        },

        customerForm_validateForm() {
            let isValid = true;
            
            // Required fields validation
            const requiredFields = [
                { name: 'firstname', message: 'Първото име е задължително' },
                { name: 'lastname', message: 'Фамилията е задължителна' },
                { name: 'email', message: 'Email адресът е задължителен' },
                { name: 'telephone', message: 'Телефонът е задължителен' }
            ];
            
            requiredFields.forEach(field => {
                const input = this.elements.form.querySelector(`[name="${field.name}"]`);
                if (input && !input.value.trim()) {
                    this.customerForm_showFieldError(field.name, field.message);
                    isValid = false;
                }
            });
            
            // Email validation
            const emailInput = this.elements.form.querySelector('[name="email"]');
            if (emailInput && emailInput.value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(emailInput.value)) {
                    this.customerForm_showFieldError('email', 'Невалиден email адрес');
                    isValid = false;
                }
            }

            // Password validation
            if (!this.customerForm_validatePasswords()) {
                isValid = false;
            }

            return isValid;
        },

        customerForm_submitForm() {
            const formData = new FormData(this.elements.form);
            
            fetch(this.elements.form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => this.customerForm_handleSubmitResponse(data))
            .catch(error => this.customerForm_handleSubmitError(error))
            .finally(() => {
                this.state.isSubmitting = false;
            });
        },

        customerForm_handleSubmitResponse(response) {
            if (response.success) {
                this.customerForm_showNotification(response.success, 'success');
                if (response.redirect) {
                    setTimeout(() => {
                        window.location.href = response.redirect;
                    }, 1500);
                }
            } else if (response.error) {
                if (typeof response.error === 'object') {
                    Object.entries(response.error).forEach(([field, message]) => {
                        this.customerForm_showFieldError(field, message);
                    });
                } else {
                    this.customerForm_showNotification(response.error, 'error');
                }
            }
        },

        customerForm_handleSubmitError(error) {
            this.customerForm_logDebug('Submit error:', error);
            this.customerForm_showNotification('Възникна грешка при запазването', 'error');
        },

        customerForm_unlockCustomer() {
            if (!this.config.unlockUrl) return;
            
            fetch(this.config.unlockUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `user_token=${this.config.userToken}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.customerForm_showNotification(data.success, 'success');
                } else if (data.error) {
                    this.customerForm_showNotification(data.error, 'error');
                }
            })
            .catch(error => {
                this.customerForm_logDebug('Unlock error:', error);
                this.customerForm_showNotification('Възникна грешка при отключването', 'error');
            });
        },

        customerForm_bindValidation() {
            // Real-time validation for email
            const emailInput = this.elements.form?.querySelector('[name="email"]');
            if (emailInput) {
                emailInput.addEventListener('blur', () => {
                    if (emailInput.value) {
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (!emailRegex.test(emailInput.value)) {
                            this.customerForm_showFieldError('email', 'Невалиден email адрес');
                        } else {
                            this.customerForm_clearFieldError('email');
                        }
                    }
                });
            }
        },

        customerForm_showFieldError(fieldName, message) {
            const errorElement = document.querySelector(`[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.classList.remove('hidden');
            }
            
            const input = this.elements.form?.querySelector(`[name="${fieldName}"]`);
            if (input) {
                input.classList.add('border-red-500');
            }
        },

        customerForm_clearFieldError(fieldName) {
            const errorElement = document.querySelector(`[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.classList.add('hidden');
            }
            
            const input = this.elements.form?.querySelector(`[name="${fieldName}"]`);
            if (input) {
                input.classList.remove('border-red-500');
            }
        },

        customerForm_showFieldError(fieldName, message) {
            const errorElement = document.querySelector(`[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.classList.remove('hidden');
            }

            const input = this.elements.form?.querySelector(`[name="${fieldName}"]`);
            if (input) {
                input.classList.add('border-red-500');
            }
        },

        customerForm_clearFieldError(fieldName) {
            const errorElement = document.querySelector(`[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.classList.add('hidden');
            }

            const input = this.elements.form?.querySelector(`[name="${fieldName}"]`);
            if (input) {
                input.classList.remove('border-red-500');
            }
        },

        customerForm_clearErrors() {
            this.elements.errorMessages.forEach(element => {
                element.classList.add('hidden');
            });

            const inputs = this.elements.form?.querySelectorAll('.border-red-500');
            inputs?.forEach(input => {
                input.classList.remove('border-red-500');
            });
        },

        customerForm_showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        /**
         * Load customer history
         */
        customerForm_loadHistory() {
            if (!this.config.historyUrl || !this.elements.historyList) {
                return;
            }

            fetch(this.config.historyUrl + '&user_token=' + this.config.userToken)
                .then(response => response.text())
                .then(html => {
                    this.elements.historyList.innerHTML = html;
                })
                .catch(error => {
                    this.customerForm_logDebug('Error loading history:', error);
                    this.elements.historyList.innerHTML = '<p class="text-gray-500">Грешка при зареждане на историята.</p>';
                });
        },

        /**
         * Add customer history
         */
        customerForm_addHistory() {
            if (!this.config.addHistoryUrl || !this.elements.historyComment) {
                return;
            }

            const comment = this.elements.historyComment.value.trim();
            if (!comment) {
                this.customerForm_showNotification('Моля въведете коментар', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('customer_id', this.config.customerId);
            formData.append('comment', comment);
            formData.append('user_token', this.config.userToken);

            fetch(this.config.addHistoryUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.customerForm_showNotification(data.success, 'success');
                    this.elements.historyComment.value = '';
                    this.customerForm_loadHistory(); // Reload history
                } else if (data.error) {
                    this.customerForm_showNotification(data.error, 'error');
                }
            })
            .catch(error => {
                this.customerForm_logDebug('Error adding history:', error);
                this.customerForm_showNotification('Грешка при добавяне на коментар', 'error');
            });
        },

        /**
         * Load customer transactions
         */
        customerForm_loadTransactions() {
            if (!this.config.transactionUrl || !this.elements.transactionList) {
                return;
            }

            fetch(this.config.transactionUrl + '&user_token=' + this.config.userToken)
                .then(response => response.text())
                .then(html => {
                    this.elements.transactionList.innerHTML = html;
                })
                .catch(error => {
                    this.customerForm_logDebug('Error loading transactions:', error);
                    this.elements.transactionList.innerHTML = '<p class="text-gray-500">Грешка при зареждане на транзакциите.</p>';
                });
        },

        /**
         * Add customer transaction
         */
        customerForm_addTransaction() {
            if (!this.config.addTransactionUrl || !this.elements.transactionDescription || !this.elements.transactionAmount) {
                return;
            }

            const description = this.elements.transactionDescription.value.trim();
            const amount = parseFloat(this.elements.transactionAmount.value);

            if (!description) {
                this.customerForm_showNotification('Моля въведете описание', 'error');
                return;
            }

            if (isNaN(amount) || amount === 0) {
                this.customerForm_showNotification('Моля въведете валидна сума', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('customer_id', this.config.customerId);
            formData.append('description', description);
            formData.append('amount', amount);
            formData.append('user_token', this.config.userToken);

            fetch(this.config.addTransactionUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.customerForm_showNotification(data.success, 'success');
                    this.elements.transactionDescription.value = '';
                    this.elements.transactionAmount.value = '';
                    this.customerForm_loadTransactions(); // Reload transactions
                } else if (data.error) {
                    this.customerForm_showNotification(data.error, 'error');
                }
            })
            .catch(error => {
                this.customerForm_logDebug('Error adding transaction:', error);
                this.customerForm_showNotification('Грешка при добавяне на транзакция', 'error');
            });
        },

        /**
         * Load customer rewards
         */
        customerForm_loadRewards() {
            if (!this.config.rewardUrl || !this.elements.rewardList) {
                return;
            }

            fetch(this.config.rewardUrl + '&user_token=' + this.config.userToken)
                .then(response => response.text())
                .then(html => {
                    this.elements.rewardList.innerHTML = html;
                })
                .catch(error => {
                    this.customerForm_logDebug('Error loading rewards:', error);
                    this.elements.rewardList.innerHTML = '<p class="text-gray-500">Грешка при зареждане на reward points.</p>';
                });
        },

        /**
         * Add customer reward points
         */
        customerForm_addReward() {
            if (!this.config.addRewardUrl || !this.elements.rewardDescription || !this.elements.rewardPoints) {
                return;
            }

            const description = this.elements.rewardDescription.value.trim();
            const points = parseInt(this.elements.rewardPoints.value);

            if (!description) {
                this.customerForm_showNotification('Моля въведете описание', 'error');
                return;
            }

            if (isNaN(points) || points === 0) {
                this.customerForm_showNotification('Моля въведете валиден брой точки', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('customer_id', this.config.customerId);
            formData.append('description', description);
            formData.append('points', points);
            formData.append('user_token', this.config.userToken);

            fetch(this.config.addRewardUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.customerForm_showNotification(data.success, 'success');
                    this.elements.rewardDescription.value = '';
                    this.elements.rewardPoints.value = '';
                    this.customerForm_loadRewards(); // Reload rewards
                } else if (data.error) {
                    this.customerForm_showNotification(data.error, 'error');
                }
            })
            .catch(error => {
                this.customerForm_logDebug('Error adding reward:', error);
                this.customerForm_showNotification('Грешка при добавяне на reward points', 'error');
            });
        },

        /**
         * Load customer IP addresses
         */
        customerForm_loadIpAddresses() {
            if (!this.config.ipUrl || !this.elements.ipList) {
                this.customerForm_logDebug('IP URL or IP list element not found', {
                    ipUrl: this.config.ipUrl,
                    ipList: this.elements.ipList
                });
                return;
            }

            const url = this.config.ipUrl + '&user_token=' + this.config.userToken;
            this.customerForm_logDebug('Loading IP addresses from:', url);

            // Show loading indicator
            this.elements.ipList.innerHTML = '<div class="flex items-center justify-center p-4"><i class="ri-loader-4-line animate-spin mr-2"></i>Зареждане...</div>';

            fetch(url)
                .then(response => {
                    this.customerForm_logDebug('IP addresses response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.text();
                })
                .then(html => {
                    this.customerForm_logDebug('IP addresses HTML received:', html.substring(0, 200) + '...');
                    this.elements.ipList.innerHTML = html;
                })
                .catch(error => {
                    this.customerForm_logDebug('Error loading IP addresses:', error);
                    this.elements.ipList.innerHTML = '<p class="text-red-500">Грешка при зареждане на IP адресите: ' + error.message + '</p>';
                });
        },

        /**
         * Toggle password fields visibility
         */
        customerForm_togglePasswordFields(event) {
            if (!this.elements.passwordFields) {
                return;
            }

            if (event.target.checked) {
                this.elements.passwordFields.style.display = 'grid';
                // Make password fields required when visible
                if (this.elements.passwordInput) {
                    this.elements.passwordInput.setAttribute('required', 'required');
                }
                if (this.elements.confirmInput) {
                    this.elements.confirmInput.setAttribute('required', 'required');
                }
            } else {
                this.elements.passwordFields.style.display = 'none';
                // Remove required attribute when hidden
                if (this.elements.passwordInput) {
                    this.elements.passwordInput.removeAttribute('required');
                    this.elements.passwordInput.value = '';
                }
                if (this.elements.confirmInput) {
                    this.elements.confirmInput.removeAttribute('required');
                    this.elements.confirmInput.value = '';
                }
            }
        },

        /**
         * Validate password fields
         */
        customerForm_validatePasswords() {
            if (!this.elements.changePasswordCheckbox || !this.elements.changePasswordCheckbox.checked) {
                return true; // Skip validation if password change is not requested
            }

            const password = this.elements.passwordInput ? this.elements.passwordInput.value : '';
            const confirm = this.elements.confirmInput ? this.elements.confirmInput.value : '';

            // Clear previous errors
            this.customerForm_clearFieldError('password');
            this.customerForm_clearFieldError('confirm');

            let isValid = true;

            if (!password) {
                this.customerForm_showFieldError('password', 'Паролата е задължителна');
                isValid = false;
            } else if (password.length < 4) {
                this.customerForm_showFieldError('password', 'Паролата трябва да бъде поне 4 символа');
                isValid = false;
            }

            if (!confirm) {
                this.customerForm_showFieldError('confirm', 'Потвърждението на паролата е задължително');
                isValid = false;
            } else if (password !== confirm) {
                this.customerForm_showFieldError('confirm', 'Паролите не съвпадат');
                isValid = false;
            }

            return isValid;
        },

        /**
         * Show field error
         */
        customerForm_showFieldError(fieldName, message) {
            const errorElement = document.querySelector(`.error-message[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.classList.remove('hidden');
            }
        },

        /**
         * Clear field error
         */
        customerForm_clearFieldError(fieldName) {
            const errorElement = document.querySelector(`.error-message[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.textContent = '';
                errorElement.classList.add('hidden');
            }
        },

        customerForm_logDebug(...args) {
            if (window.console && window.console.log) {
                console.log('[CustomerFormModule]', ...args);
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => CustomerFormModule.init());
    } else {
        CustomerFormModule.init();
    }

    // Expose module globally
    window.CustomerFormModule = CustomerFormModule;

})();
