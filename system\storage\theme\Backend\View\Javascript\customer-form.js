/**
 * Customer Form Module
 * Handles customer form validation, tabs, address management, and AJAX operations
 */
(function() {
    'use strict';

    const CustomerFormModule = {
        config: {
            userToken: '',
            customerId: 0,
            countries: [],
            unlockUrl: '',
            maxConcurrentRequests: 10,
            activeRequests: 0
        },

        elements: {
            form: null,
            tabButtons: null,
            tabContents: null,
            addAddressBtn: null,
            addressesContainer: null,
            unlockBtn: null,
            errorMessages: null
        },

        state: {
            currentTab: 'tab-general',
            addressIndex: 0,
            isSubmitting: false
        },

        init() {
            this.customerForm_loadConfig();
            this.customerForm_cacheElements();
            this.customerForm_bindEvents();
            this.customerForm_initializeTabs();
            this.customerForm_initializeAddresses();
            this.customerForm_logDebug('Customer form module initialized');
        },

        customerForm_loadConfig() {
            if (window.customerFormConfig) {
                Object.assign(this.config, window.customerFormConfig);
            }
        },

        customerForm_cacheElements() {
            this.elements.form = document.getElementById('customer-form');
            this.elements.tabButtons = document.querySelectorAll('.tab-button');
            this.elements.tabContents = document.querySelectorAll('.tab-content');
            this.elements.addAddressBtn = document.getElementById('add-address');
            this.elements.addressesContainer = document.getElementById('addresses-container');
            this.elements.unlockBtn = document.getElementById('unlock-customer');
            this.elements.errorMessages = document.querySelectorAll('.error-message');
        },

        customerForm_bindEvents() {
            // Form submission
            if (this.elements.form) {
                this.elements.form.addEventListener('submit', (e) => this.customerForm_handleSubmit(e));
            }

            // Tab switching
            this.elements.tabButtons.forEach(button => {
                button.addEventListener('click', (e) => this.customerForm_switchTab(e));
            });

            // Add address
            if (this.elements.addAddressBtn) {
                this.elements.addAddressBtn.addEventListener('click', () => this.customerForm_addAddress());
            }

            // Remove address (delegated)
            if (this.elements.addressesContainer) {
                this.elements.addressesContainer.addEventListener('click', (e) => {
                    if (e.target.closest('.remove-address')) {
                        this.customerForm_removeAddress(e);
                    }
                });
            }

            // Unlock customer
            if (this.elements.unlockBtn) {
                this.elements.unlockBtn.addEventListener('click', () => this.customerForm_unlockCustomer());
            }

            // Real-time validation
            this.customerForm_bindValidation();
        },

        customerForm_initializeTabs() {
            // Show first tab by default
            if (this.elements.tabContents.length > 0) {
                this.elements.tabContents[0].classList.remove('hidden');
            }
        },

        customerForm_initializeAddresses() {
            // Set initial address index based on existing addresses
            const existingAddresses = this.elements.addressesContainer?.querySelectorAll('.address-item');
            this.state.addressIndex = existingAddresses ? existingAddresses.length : 0;
        },

        customerForm_switchTab(event) {
            event.preventDefault();
            const targetTab = event.target.dataset.tab;
            
            // Update tab buttons
            this.elements.tabButtons.forEach(button => {
                button.classList.remove('active', 'border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            event.target.classList.add('active', 'border-primary', 'text-primary');
            event.target.classList.remove('border-transparent', 'text-gray-500');
            
            // Update tab contents
            this.elements.tabContents.forEach(content => {
                content.classList.add('hidden');
            });
            
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.remove('hidden');
                this.state.currentTab = targetTab;
            }
        },

        customerForm_addAddress() {
            const addressHtml = this.customerForm_generateAddressHtml(this.state.addressIndex);

            if (this.elements.addressesContainer) {
                // Remove "no addresses" message if exists
                const noAddressesMsg = this.elements.addressesContainer.querySelector('.text-center');
                if (noAddressesMsg) {
                    noAddressesMsg.remove();
                }

                this.elements.addressesContainer.insertAdjacentHTML('beforeend', addressHtml);
                this.state.addressIndex++;
            }
        },

        customerForm_removeAddress(event) {
            const addressItem = event.target.closest('.address-item');
            if (addressItem) {
                addressItem.remove();

                // Show "no addresses" message if no addresses left
                const remainingAddresses = this.elements.addressesContainer?.querySelectorAll('.address-item');
                if (!remainingAddresses || remainingAddresses.length === 0) {
                    this.customerForm_showNoAddressesMessage();
                }
            }
        },

        customerForm_generateAddressHtml(index) {
            return `
                <div class="address-item bg-gray-50 rounded-lg p-4 border" data-address-index="${index}">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-900">Адрес ${index + 1}</h4>
                        <button type="button" class="remove-address text-red-600 hover:text-red-800">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input type="text" name="address[${index}][firstname]" placeholder="Първо име" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][lastname]" placeholder="Фамилия" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][company]" placeholder="Компания" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][address_1]" placeholder="Адрес 1" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][address_2]" placeholder="Адрес 2" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][city]" placeholder="Град" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][postcode]" placeholder="Пощенски код" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    <div class="mt-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="address[${index}][default]" value="1" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-sm text-gray-700">Адрес по подразбиране</span>
                        </label>
                    </div>
                </div>
            `;
        },

        customerForm_switchTab(event) {
            event.preventDefault();
            const targetTab = event.target.dataset.tab;

            // Update tab buttons
            this.elements.tabButtons.forEach(button => {
                button.classList.remove('active', 'border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            event.target.classList.add('active', 'border-primary', 'text-primary');
            event.target.classList.remove('border-transparent', 'text-gray-500');

            // Update tab contents
            this.elements.tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.remove('hidden');
                this.state.currentTab = targetTab;
            }
        },

        customerForm_generateAddressHtml(index) {
            return `
                <div class="address-item bg-gray-50 rounded-lg p-4 border" data-address-index="${index}">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="font-medium text-gray-900">Адрес ${index + 1}</h4>
                        <button type="button" class="remove-address text-red-600 hover:text-red-800">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input type="text" name="address[${index}][firstname]" placeholder="Първо име" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][lastname]" placeholder="Фамилия" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][company]" placeholder="Компания" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][address_1]" placeholder="Адрес 1" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][address_2]" placeholder="Адрес 2" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][city]" placeholder="Град" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <input type="text" name="address[${index}][postcode]" placeholder="Пощенски код" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    <div class="mt-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="address[${index}][default]" value="1" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-sm text-gray-700">Адрес по подразбиране</span>
                        </label>
                    </div>
                </div>
            `;
        },

        customerForm_showNoAddressesMessage() {
            const messageHtml = `
                <div class="text-center py-8 text-gray-500">
                    <i class="ri-map-pin-line text-4xl mb-4"></i>
                    <p>Няма добавени адреси</p>
                </div>
            `;
            this.elements.addressesContainer.innerHTML = messageHtml;
        },

        customerForm_handleSubmit(event) {
            event.preventDefault();
            
            if (this.state.isSubmitting) {
                return;
            }
            
            this.customerForm_clearErrors();
            
            if (!this.customerForm_validateForm()) {
                this.customerForm_showNotification('Моля поправете грешките във формата', 'error');
                return;
            }
            
            this.state.isSubmitting = true;
            this.customerForm_submitForm();
        },

        customerForm_validateForm() {
            let isValid = true;
            
            // Required fields validation
            const requiredFields = [
                { name: 'firstname', message: 'Първото име е задължително' },
                { name: 'lastname', message: 'Фамилията е задължителна' },
                { name: 'email', message: 'Email адресът е задължителен' },
                { name: 'telephone', message: 'Телефонът е задължителен' }
            ];
            
            requiredFields.forEach(field => {
                const input = this.elements.form.querySelector(`[name="${field.name}"]`);
                if (input && !input.value.trim()) {
                    this.customerForm_showFieldError(field.name, field.message);
                    isValid = false;
                }
            });
            
            // Email validation
            const emailInput = this.elements.form.querySelector('[name="email"]');
            if (emailInput && emailInput.value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(emailInput.value)) {
                    this.customerForm_showFieldError('email', 'Невалиден email адрес');
                    isValid = false;
                }
            }
            
            return isValid;
        },

        customerForm_submitForm() {
            const formData = new FormData(this.elements.form);
            
            fetch(this.elements.form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => this.customerForm_handleSubmitResponse(data))
            .catch(error => this.customerForm_handleSubmitError(error))
            .finally(() => {
                this.state.isSubmitting = false;
            });
        },

        customerForm_handleSubmitResponse(response) {
            if (response.success) {
                this.customerForm_showNotification(response.success, 'success');
                if (response.redirect) {
                    setTimeout(() => {
                        window.location.href = response.redirect;
                    }, 1500);
                }
            } else if (response.error) {
                if (typeof response.error === 'object') {
                    Object.entries(response.error).forEach(([field, message]) => {
                        this.customerForm_showFieldError(field, message);
                    });
                } else {
                    this.customerForm_showNotification(response.error, 'error');
                }
            }
        },

        customerForm_handleSubmitError(error) {
            this.customerForm_logDebug('Submit error:', error);
            this.customerForm_showNotification('Възникна грешка при запазването', 'error');
        },

        customerForm_unlockCustomer() {
            if (!this.config.unlockUrl) return;
            
            fetch(this.config.unlockUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `user_token=${this.config.userToken}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.customerForm_showNotification(data.success, 'success');
                } else if (data.error) {
                    this.customerForm_showNotification(data.error, 'error');
                }
            })
            .catch(error => {
                this.customerForm_logDebug('Unlock error:', error);
                this.customerForm_showNotification('Възникна грешка при отключването', 'error');
            });
        },

        customerForm_bindValidation() {
            // Real-time validation for email
            const emailInput = this.elements.form?.querySelector('[name="email"]');
            if (emailInput) {
                emailInput.addEventListener('blur', () => {
                    if (emailInput.value) {
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (!emailRegex.test(emailInput.value)) {
                            this.customerForm_showFieldError('email', 'Невалиден email адрес');
                        } else {
                            this.customerForm_clearFieldError('email');
                        }
                    }
                });
            }
        },

        customerForm_showFieldError(fieldName, message) {
            const errorElement = document.querySelector(`[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.classList.remove('hidden');
            }
            
            const input = this.elements.form?.querySelector(`[name="${fieldName}"]`);
            if (input) {
                input.classList.add('border-red-500');
            }
        },

        customerForm_clearFieldError(fieldName) {
            const errorElement = document.querySelector(`[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.classList.add('hidden');
            }
            
            const input = this.elements.form?.querySelector(`[name="${fieldName}"]`);
            if (input) {
                input.classList.remove('border-red-500');
            }
        },

        customerForm_showFieldError(fieldName, message) {
            const errorElement = document.querySelector(`[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.classList.remove('hidden');
            }

            const input = this.elements.form?.querySelector(`[name="${fieldName}"]`);
            if (input) {
                input.classList.add('border-red-500');
            }
        },

        customerForm_clearFieldError(fieldName) {
            const errorElement = document.querySelector(`[data-field="${fieldName}"]`);
            if (errorElement) {
                errorElement.classList.add('hidden');
            }

            const input = this.elements.form?.querySelector(`[name="${fieldName}"]`);
            if (input) {
                input.classList.remove('border-red-500');
            }
        },

        customerForm_clearErrors() {
            this.elements.errorMessages.forEach(element => {
                element.classList.add('hidden');
            });

            const inputs = this.elements.form?.querySelectorAll('.border-red-500');
            inputs?.forEach(input => {
                input.classList.remove('border-red-500');
            });
        },

        customerForm_showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        customerForm_logDebug(...args) {
            if (window.console && window.console.log) {
                console.log('[CustomerFormModule]', ...args);
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => CustomerFormModule.init());
    } else {
        CustomerFormModule.init();
    }

    // Expose module globally
    window.CustomerFormModule = CustomerFormModule;

})();
