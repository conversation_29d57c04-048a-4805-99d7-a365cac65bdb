<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Save extends \Theme25\ControllerSubMethods {

    private $error = [];

    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Изпълнява запазването на клиент
     */
    public function execute() {
        $json = [];

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->load->model('customer/customer');

            $customer_id = (int)$this->requestPost('customer_id', 0);

            if ($customer_id) {
                // Редактиране на съществуващ клиент
                $this->model_customer_customer->editCustomer($customer_id, $this->request->post);
                $json['success'] = 'Клиентът беше успешно обновен!';
            } else {
                // Добавяне на нов клиент
                $customer_id = $this->model_customer_customer->addCustomer($this->request->post);
                $json['success'] = 'Клиентът беше успешно добавен!';
            }

            $json['redirect'] = $this->getAdminLink('customer/customer');
        } else {
            $json['error'] = $this->error;
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Валидира формата за клиент
     */
    protected function validateForm() {
        if (!$this->hasPermission('modify', 'customer/customer')) {
            $this->error['warning'] = 'Нямате права за модифициране на клиенти!';
        }

        if ((utf8_strlen($this->requestPost('firstname')) < 1) || (utf8_strlen($this->requestPost('firstname')) > 32)) {
            $this->error['firstname'] = 'Първото име трябва да бъде между 1 и 32 символа!';
        }

        if ((utf8_strlen($this->requestPost('lastname')) < 1) || (utf8_strlen($this->requestPost('lastname')) > 32)) {
            $this->error['lastname'] = 'Фамилията трябва да бъде между 1 и 32 символа!';
        }

        if ((utf8_strlen($this->requestPost('email')) > 96) || !filter_var($this->requestPost('email'), FILTER_VALIDATE_EMAIL)) {
            $this->error['email'] = 'E-Mail адресът не изглежда валиден!';
        }

        // Проверка за уникалност на email
        $customer_id = (int)$this->requestPost('customer_id', 0);
        $this->load->model('customer/customer');
        
        $customer_info = $this->model_customer_customer->getCustomerByEmail($this->requestPost('email'));
        
        if ($customer_info && (!$customer_id || ($customer_info['customer_id'] != $customer_id))) {
            $this->error['warning'] = 'E-Mail адресът вече се използва!';
        }

        if ((utf8_strlen($this->requestPost('telephone')) < 3) || (utf8_strlen($this->requestPost('telephone')) > 32)) {
            $this->error['telephone'] = 'Телефонът трябва да бъде между 3 и 32 символа!';
        }

        // Валидация на персонализираните полета
        $this->load->model('customer/custom_field');
        $custom_fields = $this->model_customer_custom_field->getCustomFields(['filter_customer' => 1]);

        foreach ($custom_fields as $custom_field) {
            if ($custom_field['location'] == 'account') {
                if ($custom_field['required'] && empty($this->requestPost('custom_field')[$custom_field['location']][$custom_field['custom_field_id']])) {
                    $this->error['custom_field'][$custom_field['custom_field_id']] = sprintf('%s е задължително!', $custom_field['name']);
                } elseif (($custom_field['type'] == 'text') && !empty($custom_field['validation']) && !filter_var($this->requestPost('custom_field')[$custom_field['location']][$custom_field['custom_field_id']], FILTER_VALIDATE_REGEXP, ['options' => ['regexp' => $custom_field['validation']]])) {
                    $this->error['custom_field'][$custom_field['custom_field_id']] = sprintf('%s не е валидно!', $custom_field['name']);
                }
            }
        }

        // Валидация на адресите
        if (isset($this->request->post['address'])) {
            foreach ($this->request->post['address'] as $key => $address) {
                if ((utf8_strlen($address['firstname']) < 1) || (utf8_strlen($address['firstname']) > 32)) {
                    $this->error['address'][$key]['firstname'] = 'Първото име трябва да бъде между 1 и 32 символа!';
                }

                if ((utf8_strlen($address['lastname']) < 1) || (utf8_strlen($address['lastname']) > 32)) {
                    $this->error['address'][$key]['lastname'] = 'Фамилията трябва да бъде между 1 и 32 символа!';
                }

                if ((utf8_strlen($address['address_1']) < 3) || (utf8_strlen($address['address_1']) > 128)) {
                    $this->error['address'][$key]['address_1'] = 'Адресът трябва да бъде между 3 и 128 символа!';
                }

                if ((utf8_strlen($address['city']) < 2) || (utf8_strlen($address['city']) > 128)) {
                    $this->error['address'][$key]['city'] = 'Градът трябва да бъде между 2 и 128 символа!';
                }

                $this->load->model('localisation/country');
                $country_info = $this->model_localisation_country->getCountry($address['country_id']);

                if ($country_info && $country_info['postcode_required'] && (utf8_strlen($address['postcode']) < 2 || utf8_strlen($address['postcode']) > 10)) {
                    $this->error['address'][$key]['postcode'] = 'Пощенският код трябва да бъде между 2 и 10 символа!';
                }

                if ($address['country_id'] == '') {
                    $this->error['address'][$key]['country'] = 'Моля изберете страна!';
                }

                if (!isset($address['zone_id']) || $address['zone_id'] == '' || !is_numeric($address['zone_id'])) {
                    $this->error['address'][$key]['zone'] = 'Моля изберете регион / щат!';
                }

                // Валидация на персонализираните полета за адреси
                foreach ($custom_fields as $custom_field) {
                    if ($custom_field['location'] == 'address') {
                        if ($custom_field['required'] && empty($address['custom_field'][$custom_field['custom_field_id']])) {
                            $this->error['address'][$key]['custom_field'][$custom_field['custom_field_id']] = sprintf('%s е задължително!', $custom_field['name']);
                        } elseif (($custom_field['type'] == 'text') && !empty($custom_field['validation']) && !filter_var($address['custom_field'][$custom_field['custom_field_id']], FILTER_VALIDATE_REGEXP, ['options' => ['regexp' => $custom_field['validation']]])) {
                            $this->error['address'][$key]['custom_field'][$custom_field['custom_field_id']] = sprintf('%s не е валидно!', $custom_field['name']);
                        }
                    }
                }
            }
        }

        return !$this->error;
    }
}
