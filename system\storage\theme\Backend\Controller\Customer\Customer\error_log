[20-Jul-2025 05:44:10 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getTransactions() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php:109
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php(43): Theme25\Backend\Controller\Customer\Customer\Transaction->getTransactions(8981)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer.php(115): Theme25\Backend\Controller\Customer\Customer\Transaction->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Customer->transaction()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(213): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'transaction', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'transaction', Array)
#6 /home/<USER>/storage_theme25/modificati in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php on line 109
[20-Jul-2025 05:47:37 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getTransactions() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php:109
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php(43): Theme25\Backend\Controller\Customer\Customer\Transaction->getTransactions(8981)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer.php(115): Theme25\Backend\Controller\Customer\Customer\Transaction->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Customer->transaction()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(213): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'transaction', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'transaction', Array)
#6 /home/<USER>/storage_theme25/modificati in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php on line 109
[20-Jul-2025 05:49:08 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getTransactions() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php:109
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php(43): Theme25\Backend\Controller\Customer\Customer\Transaction->getTransactions(8903)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer.php(115): Theme25\Backend\Controller\Customer\Customer\Transaction->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Customer->transaction()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(213): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'transaction', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'transaction', Array)
#6 /home/<USER>/storage_theme25/modificati in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php on line 109
[20-Jul-2025 05:50:19 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getTransactions() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php:109
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php(43): Theme25\Backend\Controller\Customer\Customer\Transaction->getTransactions(4433)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer.php(115): Theme25\Backend\Controller\Customer\Customer\Transaction->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Customer->transaction()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(213): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'transaction', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'transaction', Array)
#6 /home/<USER>/storage_theme25/modificati in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php on line 109
[20-Jul-2025 05:50:23 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getTransactions() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php:109
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php(43): Theme25\Backend\Controller\Customer\Customer\Transaction->getTransactions(4433)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer.php(115): Theme25\Backend\Controller\Customer\Customer\Transaction->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Customer->transaction()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(213): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'transaction', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'transaction', Array)
#6 /home/<USER>/storage_theme25/modificati in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php on line 109
[20-Jul-2025 05:50:30 UTC] PHP Fatal error:  Uncaught Exception: Error: Table 'rakla_test.oc_customer_ban_ip' doesn't exist<br />Error No: 1146<br />SELECT * FROM `oc_customer_ban_ip` WHERE ip = '*************' in /home/<USER>/theme25/system/library/db/mysqli.php:40
Stack trace:
#0 /home/<USER>/theme25/system/library/db.php(16): DB\MySQLi->query('SELECT * FROM `...', Array)
#1 /home/<USER>/storage_theme25/theme/SecondDB.php(64): DB->query('SELECT * FROM `...', Array)
#2 /home/<USER>/storage_theme25/theme/Controller.php(674): Theme25\SecondDB->query('SELECT * FROM `...')
#3 [internal function]: Theme25\Controller->dbQuery('SELECT * FROM `...')
#4 /home/<USER>/storage_theme25/theme/ControllerSubMethods.php(16): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Ip.php(244): Theme25\ControllerSubMethods->__call('dbQuery', Array)
#6 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Ip.php(123): Theme25\Backend\Controller\Customer\Customer\Ip->isIpBanned('*************')
#7 /home/<USER>/home/<USER>/theme25/system/library/db/mysqli.php on line 40
[20-Jul-2025 05:50:33 UTC] PHP Fatal error:  Uncaught Error: Call to a member function getTransactions() on null in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php:109
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php(43): Theme25\Backend\Controller\Customer\Customer\Transaction->getTransactions(4433)
#1 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer.php(115): Theme25\Backend\Controller\Customer\Customer\Transaction->execute()
#2 [internal function]: Theme25\Backend\Controller\Customer\Customer->transaction()
#3 /home/<USER>/storage_theme25/theme/RequestProcessor.php(213): call_user_func_array(Array, Array)
#4 /home/<USER>/storage_theme25/theme/RequestProcessor.php(37): Theme25\RequestProcessor->callControllerAndMethod('ControllerCusto...', 'transaction', Array)
#5 /home/<USER>/theme25/system/engine/requestprocessor.php(27): Theme25\RequestProcessor->process('ControllerCusto...', 'transaction', Array)
#6 /home/<USER>/storage_theme25/modificati in /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Transaction.php on line 109
[20-Jul-2025 05:51:49 UTC] PHP Fatal error:  Uncaught Exception: Error: Table 'rakla_test.oc_customer_ban_ip' doesn't exist<br />Error No: 1146<br />SELECT * FROM `oc_customer_ban_ip` WHERE ip = '*************' in /home/<USER>/theme25/system/library/db/mysqli.php:40
Stack trace:
#0 /home/<USER>/theme25/system/library/db.php(16): DB\MySQLi->query('SELECT * FROM `...', Array)
#1 /home/<USER>/storage_theme25/theme/SecondDB.php(64): DB->query('SELECT * FROM `...', Array)
#2 /home/<USER>/storage_theme25/theme/Controller.php(674): Theme25\SecondDB->query('SELECT * FROM `...')
#3 [internal function]: Theme25\Controller->dbQuery('SELECT * FROM `...')
#4 /home/<USER>/storage_theme25/theme/ControllerSubMethods.php(16): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Ip.php(357): Theme25\ControllerSubMethods->__call('dbQuery', Array)
#6 /home/<USER>/storage_theme25/theme/Backend/Controller/Customer/Customer/Ip.php(208): Theme25\Backend\Controller\Customer\Customer\Ip->isIpBanned('*************')
#7 /home/<USER>/home/<USER>/theme25/system/library/db/mysqli.php on line 40
