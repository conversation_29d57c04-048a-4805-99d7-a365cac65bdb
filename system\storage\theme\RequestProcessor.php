<?php

namespace Theme25;

/**
 * Клас за обработка на заявки и управление на специални настройки на темата
 */
class RequestProcessor extends BaseProcessor {

    /**
     * Конструктор
     *
     * @param \Registry $registry Регистър с обекти
     * @param string $controllerPath Път до контролерите ('Backend' или 'Frontend')
     */
    public function __construct($registry, $controllerPath) {
        parent::__construct($registry, $controllerPath);

        // Добавяне на инстанцията на RequestProcessor в регистъра
        $registry->set('request_processor', $this);

        // Инициализиране на конфигурациите за двете бази данни
        if (self::$secondDbInitialized) {
            \Theme25\ConfigManager::loadFirstDbConfig($registry);
        }
    }

    /**
     * Обработва заявка към контролер
     *
     * @param string $controller Име на контролера
     * @param string $method Име на метода
     * @param array $args Аргументи за метода
     * @return mixed Резултат от изпълнението на метода
     */
    public function process($controller, $method, $args = array()) {
        return $this->callControllerAndMethod($controller, $method, $args);
    }

    /**
     * Извиква метод на контролер
     *
     * @param string $controller Име на контролера
     * @param string $method Име на метода
     * @param array $args Аргументи за метода
     * @return mixed Резултат от изпълнението на метода
     */
    private function callControllerAndMethod($controller, $method, $args = array()) {

        $theme_controller = 'Theme25\\' . $this->controllerPath . '\\' . $controller;

        preg_match('#(ControllerCustomerCustomField)$#i', $controller, $controller_match);
        preg_match('#(ControllerErrorNotFound)$#i', $controller, $error_controller_match);

        if($error_controller_match) {
            $theme_controller = 'Theme25\\' . $this->controllerPath . '\\Controller\\Error\\Notfound';
        }

        $pattern = '/(?<=[a-z])(?=[A-Z])|(?<=[A-Z])(?=[A-Z][a-z])/';
        $parts = preg_split($pattern, $theme_controller);
        $theme_controller = implode('\\', $parts);
        $original_route = !empty($this->registry->get('request')->get['route']) ? $this->registry->get('request')->get['route'] : ''; 
        

        $class_exists = false;
        $path_parts = explode('\\', $theme_controller);
        $original_route_parts = explode('/', $original_route);
        $original_method = $method;
        $backupMethod = end($original_route_parts);
        $controller_changed = false;

        $existing_theme_controllers = [];

        $log[] = print_r($original_route_parts, true);
 
        $loop = 10;
        while ($path_parts) {
            if($loop-- < 1) break;
            $test_class = implode('\\', $path_parts);
            if (class_exists($test_class)) {
                $class_exists = true;
                $existing_theme_controllers [] = $test_class;
                if(count($original_route_parts) > 3 &&$method != $backupMethod) {
                    if($controller_match)  $log[] = 'While loop: 1.backup method: ' . $method . ' => ' . $backupMethod;
                    $path_parts[] = ucfirst($method);
                    $method = $backupMethod;
                    continue;
                }
                if($controller_match)  $log[] = 'While loop: 1';
                break;
            }
            else if(count($path_parts) > 2) {
               $method = array_pop($path_parts);
               $controller_changed = true;

               if($controller_match)  $log[] = 'While loop: 2, method: ' . $method;
            }
            else if(count($path_parts) < 3) {
                $method = $original_method;
                break;
            }
        }

        if($error_controller_match) {
            $theme_controller = 'Theme25\\' . $this->controllerPath . '\\Controller\\Error\\Notfound';
        }
        
        $class_exists = class_exists($theme_controller);
        

        if($controller_match) {
            F()->log->developer($theme_controller . ' => ' . json_encode($class_exists), __FILE__, __LINE__);
            F()->log->developer($existing_theme_controllers, __FILE__, __LINE__);
        }

        if($class_exists && $existing_theme_controllers) {
            $theme_controller = array_pop($existing_theme_controllers);
        }
        else {
            // последен опит за намиране на съществуващ контролер
            $theme_controller_parts = explode('\\', $theme_controller);
            $last_part = array_pop($theme_controller_parts);


           if($controller_match)  $log[] = 'Last part: ' . $last_part;

            $theme_controller = implode('\\', $theme_controller_parts) . strtolower($last_part);

            if($controller_match)  $log[] = 'Last theme controller: ' . $theme_controller;


            $class_exists = class_exists($theme_controller);
            if($method == $last_part) $method = $original_method;
        }

        if($method == 'Index') {
            $method = 'index';
        }

        $flag = false; 
        if($controller_match) {
            $flag = true;
            $log[] = 'Controller match: ' . $controller;
            $log[] = 'Method: ' . $method;
            $log[] = 'Backup method: ' . $backupMethod;
            $log[] = 'Theme controller: ' . $theme_controller;
            $log[] = 'Class exists of theme controller: ' . json_encode($class_exists);
            $log[] = 'Path parts: '. print_r($path_parts, true);
            $log[] = 'Original route: ' . $this->registry->get('request')->get['route'];

            F()->log->developer($log, __FILE__, __LINE__);
        }

        if ($class_exists) {
            // Запазване на оригиналната база данни
            $originalDb = $this->registry->get('db');
            $usedSecondDb = false;

            if($controller_changed) {
                $controller_parts = $path_parts;
                $controller = implode('/', $controller_parts);
            }

            // Проверка дали контролерът трябва да използва втората база данни
            $controllerPath = strtolower(str_replace('Controller', '', $controller));
            $route_parts =  $path_parts;
            array_shift($route_parts); // remove 'Theme25'
            array_shift($route_parts); // remove 'Backend' or 'Frontend'
            array_shift($route_parts); // remove 'Controller'
            $route = implode('/', $route_parts);

            // if($flag) {
            //     F()->log->developer($route, __FILE__, __LINE__);
            // }

            if ($this->shouldUseSecondDb($route)) {
                // Временно заместване на базата данни в регистъра с втората база данни
                $this->registry->set('db', self::$secondDb);
                $usedSecondDb = true;

                // Указване, че в момента се използва втората база данни
                $this->switchToSecondDatabase($this->registry);

                if($flag) {
                    F()->log->developer('Switched to second db', __FILE__, __LINE__);
                }
            }
            else {
                if($flag) {
                    F()->log->developer('Not switched to second db', __FILE__, __LINE__);
                }
            }

            if($flag) {
                F()->log->developer($theme_controller, __FILE__, __LINE__);
                F()->log->developer($method, __FILE__, __LINE__);
            }
            
            $parent_theme_controller_instance = null;
            if($existing_theme_controllers) {
                $parent_theme_controller = array_pop($existing_theme_controllers);
                if($parent_theme_controller != $theme_controller) {
                    $parent_theme_controller_instance = new $parent_theme_controller($this->registry);
                }
            }

            // Създаване на инстанция на контролера
            $instance = new $theme_controller($parent_theme_controller_instance  ? $parent_theme_controller_instance : $this->registry);

            try {
                // Изпълнение на метода
                if (is_callable([$instance, $method])) {
                    $result = call_user_func_array([$instance, $method], $args);

                    // Възстановяване на оригиналната база данни, ако е била заменена
                    if ($usedSecondDb) {
                        $this->registry->set('db', $originalDb);

                        // Указване, че в момента се използва първата (основната) база данни
                        $this->switchToFirstDatabase($this->registry);
                    }

                    return $result;
                }

            } catch (\Exception $e) {
                // Възстановяване на оригиналната база данни в случай на грешка
                if ($usedSecondDb) {
                    $this->registry->set('db', $originalDb);

                    // Указване, че в момента се използва първата (основната) база данни
                    $this->switchToFirstDatabase($this->registry);
                }

                    // Записване на грешката в лог файла
                if (class_exists('\Log')) {
                    $log = new \Log('second_db_error.log');
                    $log->write($e->getMessage());
                }

                throw $e;
            }

            // Възстановяване на оригиналната база данни, ако е била заменена
            if ($usedSecondDb) {
                $this->registry->set('db', $originalDb);

                // Указване, че в момента се използва първата (основната) база данни
                $this->switchToFirstDatabase($this->registry);
            }

        }

        return -11111111111;
    }




}