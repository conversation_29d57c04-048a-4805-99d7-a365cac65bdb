<?php

namespace Theme25\Backend\Controller\Customer\Customer;

class Delete extends \Theme25\ControllerSubMethods {

    public function __construct($controller) {
        parent::__construct($controller);
        $this->setLog('customers.log');
    }

    /**
     * Изпълнява изтриването на клиент/клиенти
     */
    public function execute() {
        $json = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $json['error'] = 'Нямате права за изтриване на клиенти!';
        } else {
            $this->loadModelAs('customer/customer', 'customerModel');

            if (isset($this->request->post['selected']) && is_array($this->request->post['selected'])) {
                // Масово изтриване
                $deleted_count = 0;
                $errors = [];

                foreach ($this->request->post['selected'] as $customer_id) {
                    $customer_id = (int)$customer_id;
                    
                    if ($this->canDeleteCustomer($customer_id)) {
                        $this->customerModel->deleteCustomer($customer_id);
                        $deleted_count++;
                    } else {
                        $customer_info = $this->customerModel->getCustomer($customer_id);
                        if ($customer_info) {
                            $errors[] = sprintf('Клиентът "%s %s" не може да бъде изтрит, защото има свързани поръчки!', 
                                $customer_info['firstname'], $customer_info['lastname']);
                        }
                    }
                }

                if ($deleted_count > 0) {
                    $json['success'] = sprintf('Успешно изтрити %d клиента!', $deleted_count);
                }

                if (!empty($errors)) {
                    $json['warning'] = implode('<br>', $errors);
                }

                if ($deleted_count == 0 && empty($errors)) {
                    $json['error'] = 'Не са избрани клиенти за изтриване!';
                }
            } else {
                // Единично изтриване
                $customer_id = (int)$this->requestGet('customer_id', 0);

                if ($customer_id && $this->canDeleteCustomer($customer_id)) {
                    $customer_info = $this->customerModel->getCustomer($customer_id);
                    $this->customerModel->deleteCustomer($customer_id);
                    
                    if ($customer_info) {
                        $json['success'] = sprintf('Клиентът "%s %s" беше успешно изтрит!', 
                            $customer_info['firstname'], $customer_info['lastname']);
                    } else {
                        $json['success'] = 'Клиентът беше успешно изтрит!';
                    }
                } else {
                    $json['error'] = 'Клиентът не може да бъде изтрит, защото има свързани поръчки!';
                }
            }
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали клиентът може да бъде изтрит
     */
    private function canDeleteCustomer($customer_id) {
        // Проверка за свързани поръчки
        $this->loadModelAs('sale/order', 'orderModel');
        $orders = $this->orderModel->getOrdersByCustomerId($customer_id);

        if (!empty($orders)) {
            return false;
        }

        // Проверка за свързани транзакции
        $this->loadModelAs('customer/customer', 'customerModel');
        $transactions = $this->customerModel->getTransactions($customer_id);

        if (!empty($transactions)) {
            return false;
        }

        // Проверка за свързани награди
        $rewards = $this->customerModel->getRewards($customer_id);

        if (!empty($rewards)) {
            return false;
        }

        return true;
    }

    /**
     * Проверява дали клиентът съществува
     */
    private function customerExists($customer_id) {
        $this->loadModelAs('customer/customer', 'customerModel');
        $customer_info = $this->customerModel->getCustomer($customer_id);
        
        return !empty($customer_info);
    }

    /**
     * Валидира заявката за изтриване
     */
    private function validateDeleteRequest() {
        $errors = [];

        if (!$this->hasPermission('modify', 'customer/customer')) {
            $errors[] = 'Нямате права за изтриване на клиенти!';
        }

        $post = $this->requestPost();

        if (isset($post['selected'])) {
            if (!is_array($post['selected']) || empty($post['selected'])) {
                $errors[] = 'Не са избрани клиенти за изтриване!';
            }
        } else {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            
            if (!$customer_id) {
                $errors[] = 'Невалиден ID на клиент!';
            } elseif (!$this->customerExists($customer_id)) {
                $errors[] = 'Клиентът не съществува!';
            }
        }

        return $errors;
    }
}
