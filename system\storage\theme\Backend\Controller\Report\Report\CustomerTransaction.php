<?php

namespace Theme25\Backend\Controller\Report\Report;

class CustomerTransaction extends \Theme25\ControllerSubMethods {

    public function __construct($registry) {
        parent::__construct($registry);
        
        // Зареждане на необходимите модели
        $this->loadModel('extension/report/customer_transaction');
    }

    /**
     * Генерира отчета за транзакции на клиенти
     */
    public function report() {
        // Получаваме филтрите - същата логика като в оригиналния код
        $filter_date_start = $this->requestGet('filter_date_start', '');
        $filter_date_end = $this->requestGet('filter_date_end', '');
        $filter_customer = $this->requestGet('filter_customer', '');
        $filter_limit = $this->requestGet('limit', 20);
        $page = $this->requestGet('page', 1);

        // Подготвяме данните за заявката - същата логика като в оригиналния код
        $filter_data = [
            'filter_date_start' => $filter_date_start,
            'filter_date_end'   => $filter_date_end,
            'filter_customer'   => $filter_customer,
            'start'             => ($page - 1) * $filter_limit,
            'limit'             => $filter_limit
        ];

        // Получаваме данните от модела
        $customer_total = $this->model_extension_report_customer_transaction->getTotalTransactions($filter_data);
        $results = $this->model_extension_report_customer_transaction->getTransactions($filter_data);

        // Подготвяме данните за изгледа
        $customers = [];
        foreach ($results as $result) {
            $customers[] = [
                'customer'       => $result['customer'],
                'email'          => $result['email'],
                'customer_group' => $result['customer_group'],
                'status'         => ($result['status'] ? 'Активен' : 'Неактивен'),
                'total'          => $this->formatCurrency($result['total']),
                'edit'           => $this->getAdminLink('customer/customer/edit', 'customer_id=' . $result['customer_id'])
            ];
        }

        // Пагинация
        $pagination_data = $this->preparePagination($customer_total, $page, $filter_limit, [
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'filter_customer' => $filter_customer,
            'limit' => $filter_limit
        ]);

        // Подготвяме данните за шаблона
        $data = [
            'customers' => $customers,
            'pagination' => $pagination_data['pagination'],
            'filter_date_start' => $filter_date_start,
            'filter_date_end' => $filter_date_end,
            'filter_customer' => $filter_customer
        ];

        // Добавяме текстовете за шаблона
        $data = array_merge($data, [
            'heading_title' => 'Отчет за транзакции на клиенти',
            'button_filter' => 'Филтър',
            'button_reset' => 'Нулиране',
            'text_date_start' => 'Начална дата',
            'text_date_end' => 'Крайна дата',
            'text_customer' => 'Клиент',
            'column_customer' => 'Клиент',
            'column_email' => 'Имейл',
            'column_customer_group' => 'Група клиенти',
            'column_status' => 'Статус',
            'column_total' => 'Общо транзакции',
            'column_action' => 'Действие',
            'text_edit' => 'Редактиране',
            'text_no_results' => 'Няма данни за показване',
            'reset' => $this->getAdminLink('report/report', 'code=customer_transaction')
        ]);

        // Рендираме адаптирания шаблон
        return $this->renderPartTemplate('report/customer_transaction_info', $data);
    }

    /**
     * Подготвя пагинацията за отчета
     *
     * @param int $total Общ брой записи
     * @param int $page Текуща страница
     * @param int $limit Записи на страница
     * @param array $filters Филтри за URL
     * @return array Масив с pagination HTML и results текст
     */
    private function preparePagination($total, $page, $limit, $filters = []) {
        // Опции за брой записи на страница
        $limits = [
            ['value' => 10, 'text' => '10 на страница'],
            ['value' => 20, 'text' => '20 на страница'],
            ['value' => 50, 'text' => '50 на страница'],
            ['value' => 100, 'text' => '100 на страница']
        ];

        // Създаване и конфигуриране на обект за пагинация
        $pagination = new \Theme25\Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $limit;

        // Генериране на URL с филтри за пагинацията
        $filter_params = $this->buildReportFilterParams($filters);
        $pagination->url = $this->getAdminLink('report/report', 'code=customer_transaction' . $filter_params . '&page={page}');
        $pagination->setLimits($limits);
        $pagination->setLimitUrl($this->getAdminLink('report/report', 'code=customer_transaction' . $filter_params . '&limit={limit}'));
        $pagination->setProductText('записа');

        return [
            'pagination' => $pagination->render()
        ];
    }

    /**
     * Генерира URL параметри за филтрите на отчета
     *
     * @param array $filters Данни за филтрите
     * @return string URL параметри
     */
    private function buildReportFilterParams($filters) {
        $params = [];

        foreach ($filters as $key => $value) {
            if ($value !== '' && $value !== null && $value !== 0) {
                $params[] = $key . '=' . urlencode($value);
            }
        }

        return $params ? '&' . implode('&', $params) : '';
    }
}
