<!-- Customer Group Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
	<div class="flex flex-col md:flex-row md:items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-gray-800">
				{% if customer_group_id %}Редактиране на клиентска група{% else %}Добавяне на клиентска група{% endif %}
			</h1>
			<p class="text-gray-500 mt-1">
				{% if customer_group_id %}Редактирайте настройките на клиентската група{% else %}Създайте нова клиентска група{% endif %}
			</p>
		</div>
		<div class="flex flex-col sm:flex-row gap-3 mt-4 md:mt-0">
			<a href="{{ cancel }}" class="px-4 py-2 bg-gray-500 text-white rounded-button hover:bg-gray-600 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-arrow-left-line"></i>
				</div>
				<span>Назад</span>
			</a>
			<button type="submit" form="customer-group-form" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors whitespace-nowrap flex items-center !rounded-button">
				<div class="w-5 h-5 flex items-center justify-center mr-2">
					<i class="ri-save-line"></i>
				</div>
				<span>Запази</span>
			</button>
		</div>
	</div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
	<form id="customer-group-form" method="post" action="{{ action }}" class="space-y-6">
		<input type="hidden" name="customer_group_id" value="{{ customer_group_id }}">
		<input type="hidden" name="user_token" value="{{ user_token }}">

		<div class="max-w-4xl">
			<div class="bg-white rounded-lg shadow-sm overflow-hidden">
				<div class="p-6">
					<!-- Multi-language Names -->
					<div class="mb-8">
						<h3 class="text-lg font-medium text-gray-900 mb-4">Име на групата</h3>
						
						{% if languages|length > 1 %}
						<!-- Language Tabs -->
						<div class="border-b border-gray-200 mb-4">
							<nav class="-mb-px flex space-x-8">
								{% for language in languages %}
								<button type="button" data-lang-tab="{{ language.language_id }}" class="lang-tab {% if loop.first %}active{% endif %} py-2 px-1 border-b-2 font-medium text-sm {% if loop.first %}border-primary text-primary{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %}">
									<img src="{{ language.image }}" alt="{{ language.name }}" class="inline-block w-4 h-4 mr-2">
									{{ language.name }}
								</button>
								{% endfor %}
							</nav>
						</div>

						<!-- Language Content -->
						{% for language in languages %}
						<div id="lang-content-{{ language.language_id }}" class="lang-content {% if not loop.first %}hidden{% endif %}">
							<div class="grid grid-cols-1 gap-6">
								<!-- Group Name -->
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">
										Име на групата ({{ language.name }}) <span class="text-red-500">*</span>
									</label>
									<input type="text" name="customer_group_description[{{ language.language_id }}][name]" 
										   value="{{ customer_group_description[language.language_id].name|default('') }}" required
										   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
										   placeholder="Въведете име на групата">
									<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="name-{{ language.language_id }}"></div>
								</div>

								<!-- Group Description -->
								<div>
									<label class="block text-sm font-medium text-gray-700 mb-1">
										Описание ({{ language.name }})
									</label>
									<textarea name="customer_group_description[{{ language.language_id }}][description]" rows="3"
											  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
											  placeholder="Въведете описание на групата">{{ customer_group_description[language.language_id].description|default('') }}</textarea>
								</div>
							</div>
						</div>
						{% endfor %}
						{% else %}
						<!-- Single Language -->
						{% set language = languages|first %}
						<div class="grid grid-cols-1 gap-6">
							<!-- Group Name -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Име на групата <span class="text-red-500">*</span>
								</label>
								<input type="text" name="customer_group_description[{{ language.language_id }}][name]" 
									   value="{{ customer_group_description[language.language_id].name|default('') }}" required
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="Въведете име на групата">
								<div class="error-message text-red-500 text-sm mt-1 hidden" data-field="name"></div>
							</div>

							<!-- Group Description -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
								<textarea name="customer_group_description[{{ language.language_id }}][description]" rows="3"
										  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
										  placeholder="Въведете описание на групата">{{ customer_group_description[language.language_id].description|default('') }}</textarea>
							</div>
						</div>
						{% endif %}
					</div>

					<!-- Group Settings -->
					<div class="pt-6 border-t border-gray-200">
						<h3 class="text-lg font-medium text-gray-900 mb-4">Настройки на групата</h3>
						
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<!-- Approval Required -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-3">Изисква одобрение</label>
								<div class="space-y-2">
									<label class="flex items-center">
										<input type="radio" name="approval" value="1" {% if approval %}checked{% endif %}
											   class="text-primary focus:ring-primary border-gray-300">
										<span class="ml-2 text-sm text-gray-700">Да - новите клиенти трябва да бъдат одобрени</span>
									</label>
									<label class="flex items-center">
										<input type="radio" name="approval" value="0" {% if not approval %}checked{% endif %}
											   class="text-primary focus:ring-primary border-gray-300">
										<span class="ml-2 text-sm text-gray-700">Не - автоматично одобрение</span>
									</label>
								</div>
								<p class="text-xs text-gray-500 mt-2">
									Ако е избрано "Да", новите клиенти от тази група ще трябва да бъдат одобрени ръчно преди да могат да влизат в системата.
								</p>
							</div>

							<!-- Sort Order -->
							<div>
								<label class="block text-sm font-medium text-gray-700 mb-1">
									Ред на сортиране
								</label>
								<input type="number" name="sort_order" value="{{ sort_order|default(1) }}" min="0"
									   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
									   placeholder="0">
								<p class="text-xs text-gray-500 mt-1">
									По-ниските числа се показват първи в списъците.
								</p>
							</div>
						</div>
					</div>

					{% if customer_group_id %}
					<!-- Group Information -->
					<div class="mt-6 pt-6 border-t border-gray-200">
						<h3 class="text-lg font-medium text-gray-900 mb-4">Информация за групата</h3>
						<div class="bg-gray-50 rounded-lg p-4">
							<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
								<div>
									<span class="font-medium text-gray-700">ID на групата:</span>
									<span class="text-gray-900">#{{ customer_group_id }}</span>
								</div>
								<div>
									<span class="font-medium text-gray-700">Брой клиенти:</span>
									<span class="text-gray-900">{{ customer_count|default(0) }}</span>
								</div>
							</div>
						</div>
					</div>
					{% endif %}

					<!-- Additional Settings -->
					<div class="mt-6 pt-6 border-t border-gray-200">
						<h3 class="text-lg font-medium text-gray-900 mb-4">Допълнителни настройки</h3>
						
						<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
							<div class="flex">
								<div class="flex-shrink-0">
									<i class="ri-information-line text-blue-400"></i>
								</div>
								<div class="ml-3">
									<h4 class="text-sm font-medium text-blue-800">Информация</h4>
									<div class="mt-2 text-sm text-blue-700">
										<ul class="list-disc list-inside space-y-1">
											<li>Клиентските групи определят различни нива на достъп и привилегии</li>
											<li>Можете да настроите различни цени за различни групи</li>
											<li>Групите с изискване за одобрение са подходящи за B2B клиенти</li>
											<li>Редът на сортиране определя как групите се показват в списъците</li>
										</ul>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</main>

<script>
// Конфигурация за страницата
window.customerGroupFormConfig = {
	userToken: '{{ user_token }}',
	customerGroupId: {{ customer_group_id|default(0) }},
	languages: {{ languages|json_encode|raw }}
};

// Language tabs functionality
document.addEventListener('DOMContentLoaded', function() {
	const langTabs = document.querySelectorAll('.lang-tab');
	const langContents = document.querySelectorAll('.lang-content');

	langTabs.forEach(tab => {
		tab.addEventListener('click', function() {
			const langId = this.dataset.langTab;
			
			// Remove active class from all tabs
			langTabs.forEach(t => {
				t.classList.remove('active', 'border-primary', 'text-primary');
				t.classList.add('border-transparent', 'text-gray-500');
			});
			
			// Add active class to clicked tab
			this.classList.add('active', 'border-primary', 'text-primary');
			this.classList.remove('border-transparent', 'text-gray-500');
			
			// Hide all content
			langContents.forEach(content => {
				content.classList.add('hidden');
			});
			
			// Show selected content
			const targetContent = document.getElementById('lang-content-' + langId);
			if (targetContent) {
				targetContent.classList.remove('hidden');
			}
		});
	});
});
</script>
