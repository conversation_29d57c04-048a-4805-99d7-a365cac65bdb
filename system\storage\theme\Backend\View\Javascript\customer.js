/**
 * Customer Management Module
 * Handles customer listing, filtering, sorting, and bulk operations
 */
(function() {
    'use strict';

    // Base module definition
    const CustomerModule = {
        // Configuration
        config: {
            userToken: '',
            deleteUrl: '',
            currentSort: 'name',
            currentOrder: 'ASC',
            maxConcurrentRequests: 10,
            requestQueue: [],
            activeRequests: 0
        },

        // DOM elements cache
        elements: {
            selectAll: null,
            selectAllHeader: null,
            customerCheckboxes: null,
            bulkActions: null,
            bulkDelete: null,
            deleteModal: null,
            confirmDelete: null,
            cancelDelete: null,
            deleteMessage: null,
            filterInputs: null,
            applyFilters: null,
            clearFilters: null,
            sortHeaders: null,
            deleteButtons: null
        },

        // State management
        state: {
            selectedCustomers: new Set(),
            currentFilters: {},
            isLoading: false
        },

        /**
         * Initialize the module
         */
        init() {
            this.customer_loadConfig();
            this.customer_cacheElements();
            this.customer_bindEvents();
            this.customer_initializeState();
            this.customer_logDebug('Customer module initialized');
        },

        /**
         * Load configuration from global window object
         */
        customer_loadConfig() {
            if (window.customerConfig) {
                Object.assign(this.config, window.customerConfig);
            }
        },

        /**
         * Cache DOM elements for better performance
         */
        customer_cacheElements() {
            this.elements.selectAll = document.getElementById('select-all');
            this.elements.selectAllHeader = document.getElementById('select-all-header');
            this.elements.customerCheckboxes = document.querySelectorAll('.customer-checkbox');
            this.elements.bulkActions = document.querySelector('.bulk-actions');
            this.elements.bulkDelete = document.getElementById('bulk-delete');
            this.elements.deleteModal = document.getElementById('delete-modal');
            this.elements.confirmDelete = document.getElementById('confirm-delete');
            this.elements.cancelDelete = document.getElementById('cancel-delete');
            this.elements.deleteMessage = document.getElementById('delete-message');
            this.elements.filterInputs = {
                name: document.getElementById('filter-name'),
                email: document.getElementById('filter-email'),
                customerGroup: document.getElementById('filter-customer-group'),
                status: document.getElementById('filter-status'),
                ip: document.getElementById('filter-ip'),
                dateAdded: document.getElementById('filter-date-added')
            };
            this.elements.applyFilters = document.getElementById('apply-filters');
            this.elements.clearFilters = document.getElementById('clear-filters');
            this.elements.sortHeaders = document.querySelectorAll('[data-sort]');
            this.elements.deleteButtons = document.querySelectorAll('.delete-customer');
        },

        /**
         * Bind event listeners
         */
        customer_bindEvents() {
            // Select all functionality
            if (this.elements.selectAll) {
                this.elements.selectAll.addEventListener('change', (e) => this.customer_handleSelectAll(e));
            }
            if (this.elements.selectAllHeader) {
                this.elements.selectAllHeader.addEventListener('change', (e) => this.customer_handleSelectAll(e));
            }

            // Individual checkboxes
            this.elements.customerCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => this.customer_handleCheckboxChange(e));
            });

            // Bulk delete
            if (this.elements.bulkDelete) {
                this.elements.bulkDelete.addEventListener('click', () => this.customer_handleBulkDelete());
            }

            // Delete modal
            if (this.elements.confirmDelete) {
                this.elements.confirmDelete.addEventListener('click', () => this.customer_confirmDelete());
            }
            if (this.elements.cancelDelete) {
                this.elements.cancelDelete.addEventListener('click', () => this.customer_hideModal());
            }

            // Filter functionality
            if (this.elements.applyFilters) {
                this.elements.applyFilters.addEventListener('click', () => this.customer_applyFilters());
            }
            if (this.elements.clearFilters) {
                this.elements.clearFilters.addEventListener('click', () => this.customer_clearFilters());
            }

            // Enter key on filter inputs
            Object.values(this.elements.filterInputs).forEach(input => {
                if (input) {
                    input.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.customer_applyFilters();
                        }
                    });
                }
            });

            // Sort headers
            this.elements.sortHeaders.forEach(header => {
                header.addEventListener('click', (e) => this.customer_handleSort(e));
            });

            // Individual delete buttons
            this.elements.deleteButtons.forEach(button => {
                button.addEventListener('click', (e) => this.customer_handleSingleDelete(e));
            });

            // Modal backdrop click
            if (this.elements.deleteModal) {
                this.elements.deleteModal.addEventListener('click', (e) => {
                    if (e.target === this.elements.deleteModal) {
                        this.customer_hideModal();
                    }
                });
            }

            // Escape key to close modal
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && !this.elements.deleteModal.classList.contains('hidden')) {
                    this.customer_hideModal();
                }
            });
        },

        /**
         * Initialize component state
         */
        customer_initializeState() {
            this.customer_updateBulkActionsVisibility();
            this.customer_loadCurrentFilters();
        },

        /**
         * Handle select all checkbox
         */
        customer_handleSelectAll(event) {
            const isChecked = event.target.checked;
            
            this.elements.customerCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const customerId = checkbox.value;
                
                if (isChecked) {
                    this.state.selectedCustomers.add(customerId);
                } else {
                    this.state.selectedCustomers.delete(customerId);
                }
            });

            // Sync both select all checkboxes
            if (this.elements.selectAll && event.target !== this.elements.selectAll) {
                this.elements.selectAll.checked = isChecked;
            }
            if (this.elements.selectAllHeader && event.target !== this.elements.selectAllHeader) {
                this.elements.selectAllHeader.checked = isChecked;
            }

            this.customer_updateBulkActionsVisibility();
        },

        /**
         * Handle individual checkbox change
         */
        customer_handleCheckboxChange(event) {
            const customerId = event.target.value;
            const isChecked = event.target.checked;

            if (isChecked) {
                this.state.selectedCustomers.add(customerId);
            } else {
                this.state.selectedCustomers.delete(customerId);
            }

            // Update select all checkboxes
            const allChecked = this.elements.customerCheckboxes.length > 0 && 
                             Array.from(this.elements.customerCheckboxes).every(cb => cb.checked);
            
            if (this.elements.selectAll) {
                this.elements.selectAll.checked = allChecked;
            }
            if (this.elements.selectAllHeader) {
                this.elements.selectAllHeader.checked = allChecked;
            }

            this.customer_updateBulkActionsVisibility();
        },

        /**
         * Update bulk actions visibility
         */
        customer_updateBulkActionsVisibility() {
            if (this.elements.bulkActions) {
                if (this.state.selectedCustomers.size > 0) {
                    this.elements.bulkActions.classList.remove('hidden');
                } else {
                    this.elements.bulkActions.classList.add('hidden');
                }
            }
        },

        /**
         * Handle bulk delete
         */
        customer_handleBulkDelete() {
            if (this.state.selectedCustomers.size === 0) {
                this.customer_showNotification('Моля изберете клиенти за изтриване', 'warning');
                return;
            }

            const count = this.state.selectedCustomers.size;
            this.elements.deleteMessage.textContent = 
                `Сигурни ли сте, че искате да изтриете ${count} избрани клиента? Това действие не може да бъде отменено.`;
            
            this.customer_showModal();
        },

        /**
         * Handle single customer delete
         */
        customer_handleSingleDelete(event) {
            const customerId = event.target.closest('[data-customer-id]').dataset.customerId;
            this.state.selectedCustomers.clear();
            this.state.selectedCustomers.add(customerId);
            
            this.elements.deleteMessage.textContent = 
                'Сигурни ли сте, че искате да изтриете този клиент? Това действие не може да бъде отменено.';
            
            this.customer_showModal();
        },

        /**
         * Confirm delete action
         */
        customer_confirmDelete() {
            if (this.state.selectedCustomers.size === 0) return;

            this.customer_hideModal();
            this.customer_performDelete(Array.from(this.state.selectedCustomers));
        },

        /**
         * Perform delete operation with AJAX throttling
         */
        customer_performDelete(customerIds) {
            const request = {
                url: this.config.deleteUrl,
                method: 'POST',
                data: {
                    selected: customerIds,
                    user_token: this.config.userToken
                },
                onSuccess: (response) => this.customer_handleDeleteResponse(response),
                onError: (error) => this.customer_handleDeleteError(error)
            };

            this.customer_queueRequest(request);
        },

        /**
         * Handle select all checkbox
         */
        customer_handleSelectAll(event) {
            const isChecked = event.target.checked;

            this.elements.customerCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const customerId = checkbox.value;

                if (isChecked) {
                    this.state.selectedCustomers.add(customerId);
                } else {
                    this.state.selectedCustomers.delete(customerId);
                }
            });

            // Sync both select all checkboxes
            if (this.elements.selectAll && event.target !== this.elements.selectAll) {
                this.elements.selectAll.checked = isChecked;
            }
            if (this.elements.selectAllHeader && event.target !== this.elements.selectAllHeader) {
                this.elements.selectAllHeader.checked = isChecked;
            }

            this.customer_updateBulkActionsVisibility();
        },

        /**
         * Handle individual checkbox change
         */
        customer_handleCheckboxChange(event) {
            const customerId = event.target.value;
            const isChecked = event.target.checked;

            if (isChecked) {
                this.state.selectedCustomers.add(customerId);
            } else {
                this.state.selectedCustomers.delete(customerId);
            }

            // Update select all checkboxes
            const allChecked = this.elements.customerCheckboxes.length > 0 &&
                             Array.from(this.elements.customerCheckboxes).every(cb => cb.checked);

            if (this.elements.selectAll) {
                this.elements.selectAll.checked = allChecked;
            }
            if (this.elements.selectAllHeader) {
                this.elements.selectAllHeader.checked = allChecked;
            }

            this.customer_updateBulkActionsVisibility();
        },

        /**
         * Handle single customer delete
         */
        customer_handleSingleDelete(event) {
            const customerId = event.target.closest('[data-customer-id]').dataset.customerId;
            this.state.selectedCustomers.clear();
            this.state.selectedCustomers.add(customerId);

            this.elements.deleteMessage.textContent =
                'Сигурни ли сте, че искате да изтриете този клиент? Това действие не може да бъде отменено.';

            this.customer_showModal();
        },

        /**
         * Handle column sorting
         */
        customer_handleSort(event) {
            const sortField = event.currentTarget.dataset.sort;
            const url = new URL(window.location);

            let newOrder = 'ASC';
            if (url.searchParams.get('sort') === sortField && url.searchParams.get('order') === 'ASC') {
                newOrder = 'DESC';
            }

            url.searchParams.set('sort', sortField);
            url.searchParams.set('order', newOrder);
            url.searchParams.delete('page');

            window.location.href = url.toString();
        },

        /**
         * Queue AJAX request with throttling
         */
        customer_queueRequest(request) {
            if (this.config.activeRequests < this.config.maxConcurrentRequests) {
                this.customer_executeRequest(request);
            } else {
                this.config.requestQueue.push(request);
            }
        },

        /**
         * Execute AJAX request
         */
        customer_executeRequest(request) {
            this.config.activeRequests++;
            
            const formData = new FormData();
            Object.keys(request.data).forEach(key => {
                if (Array.isArray(request.data[key])) {
                    request.data[key].forEach(value => {
                        formData.append(key + '[]', value);
                    });
                } else {
                    formData.append(key, request.data[key]);
                }
            });

            fetch(request.url, {
                method: request.method,
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                request.onSuccess(data);
            })
            .catch(error => {
                request.onError(error);
            })
            .finally(() => {
                this.config.activeRequests--;
                this.customer_processQueue();
            });
        },

        /**
         * Process request queue
         */
        customer_processQueue() {
            if (this.config.requestQueue.length > 0 &&
                this.config.activeRequests < this.config.maxConcurrentRequests) {
                const nextRequest = this.config.requestQueue.shift();
                this.customer_executeRequest(nextRequest);
            }
        },

        /**
         * Handle delete response
         */
        customer_handleDeleteResponse(response) {
            if (response.success) {
                this.customer_showNotification(response.success, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else if (response.error) {
                this.customer_showNotification(response.error, 'error');
            }
        },

        /**
         * Handle delete error
         */
        customer_handleDeleteError(error) {
            this.customer_logDebug('Delete error:', error);
            this.customer_showNotification('Възникна грешка при изтриването', 'error');
        },

        /**
         * Show notification
         */
        customer_showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        /**
         * Debug logging
         */
        customer_logDebug(...args) {
            if (window.console && window.console.log) {
                console.log('[CustomerModule]', ...args);
            }
        },

        /**
         * Apply filters
         */
        customer_applyFilters() {
            const filters = this.customer_collectFilters();
            const url = new URL(window.location);

            // Clear existing filter parameters
            Object.keys(filters).forEach(key => {
                url.searchParams.delete('filter_' + key);
            });

            // Add new filter parameters
            Object.entries(filters).forEach(([key, value]) => {
                if (value) {
                    url.searchParams.set('filter_' + key, value);
                }
            });

            // Reset to first page
            url.searchParams.delete('page');

            window.location.href = url.toString();
        },

        /**
         * Clear all filters
         */
        customer_clearFilters() {
            Object.values(this.elements.filterInputs).forEach(input => {
                if (input) {
                    input.value = '';
                }
            });

            const url = new URL(window.location);
            const filterParams = Array.from(url.searchParams.keys()).filter(key => key.startsWith('filter_'));
            filterParams.forEach(param => url.searchParams.delete(param));
            url.searchParams.delete('page');

            window.location.href = url.toString();
        },

        /**
         * Collect current filter values
         */
        customer_collectFilters() {
            return {
                name: this.elements.filterInputs.name?.value || '',
                email: this.elements.filterInputs.email?.value || '',
                customer_group_id: this.elements.filterInputs.customerGroup?.value || '',
                status: this.elements.filterInputs.status?.value || '',
                ip: this.elements.filterInputs.ip?.value || '',
                date_added: this.elements.filterInputs.dateAdded?.value || ''
            };
        },

        /**
         * Load current filters from URL
         */
        customer_loadCurrentFilters() {
            const url = new URL(window.location);
            Object.entries(this.elements.filterInputs).forEach(([key, input]) => {
                if (input) {
                    const paramName = 'filter_' + (key === 'customerGroup' ? 'customer_group_id' :
                                                   key === 'dateAdded' ? 'date_added' : key);
                    const value = url.searchParams.get(paramName);
                    if (value) {
                        input.value = value;
                    }
                }
            });
        },

        /**
         * Show modal
         */
        customer_showModal() {
            if (this.elements.deleteModal) {
                this.elements.deleteModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        },

        /**
         * Hide modal
         */
        customer_hideModal() {
            if (this.elements.deleteModal) {
                this.elements.deleteModal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        },

        /**
         * Show notification
         */
        customer_showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        /**
         * Debug logging
         */
        customer_logDebug(...args) {
            if (window.console && window.console.log) {
                console.log('[CustomerModule]', ...args);
            }
        }
    };

    // Extend with additional functionality using Object.assign
    Object.assign(CustomerModule, {
        // Additional methods can be added here
        customer_exportData() {
            // Future implementation for data export
            this.customer_logDebug('Export functionality not yet implemented');
        },

        customer_importData() {
            // Future implementation for data import
            this.customer_logDebug('Import functionality not yet implemented');
        }
    });

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => CustomerModule.init());
    } else {
        CustomerModule.init();
    }

    // Expose module globally for debugging
    window.CustomerModule = CustomerModule;

})();
