/**
 * Customer Group Module
 * Handles customer group listing, sorting, and bulk operations
 */
(function() {
    'use strict';

    const CustomerGroupModule = {
        config: {
            userToken: '',
            deleteUrl: '',
            currentSort: 'name',
            currentOrder: 'ASC',
            maxConcurrentRequests: 10,
            activeRequests: 0
        },

        elements: {
            selectAll: null,
            selectAllHeader: null,
            groupCheckboxes: null,
            bulkActions: null,
            bulkDelete: null,
            deleteModal: null,
            confirmDelete: null,
            cancelDelete: null,
            deleteMessage: null,
            sortHeaders: null,
            deleteButtons: null
        },

        state: {
            selectedGroups: new Set(),
            isLoading: false
        },

        init() {
            this.customerGroup_loadConfig();
            this.customerGroup_cacheElements();
            this.customerGroup_bindEvents();
            this.customerGroup_initializeState();
            this.customerGroup_logDebug('Customer group module initialized');
        },

        customerGroup_loadConfig() {
            if (window.customerGroupConfig) {
                Object.assign(this.config, window.customerGroupConfig);
            }
        },

        customerGroup_cacheElements() {
            this.elements.selectAll = document.getElementById('select-all');
            this.elements.selectAllHeader = document.getElementById('select-all-header');
            this.elements.groupCheckboxes = document.querySelectorAll('.customer-group-checkbox');
            this.elements.bulkActions = document.querySelector('.bulk-actions');
            this.elements.bulkDelete = document.getElementById('bulk-delete');
            this.elements.deleteModal = document.getElementById('delete-modal');
            this.elements.confirmDelete = document.getElementById('confirm-delete');
            this.elements.cancelDelete = document.getElementById('cancel-delete');
            this.elements.deleteMessage = document.getElementById('delete-message');
            this.elements.sortHeaders = document.querySelectorAll('[data-sort]');
            this.elements.deleteButtons = document.querySelectorAll('.delete-customer-group');
        },

        customerGroup_bindEvents() {
            // Select all functionality
            if (this.elements.selectAll) {
                this.elements.selectAll.addEventListener('change', (e) => this.customerGroup_handleSelectAll(e));
            }
            if (this.elements.selectAllHeader) {
                this.elements.selectAllHeader.addEventListener('change', (e) => this.customerGroup_handleSelectAll(e));
            }

            // Individual checkboxes
            this.elements.groupCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => this.customerGroup_handleCheckboxChange(e));
            });

            // Bulk delete
            if (this.elements.bulkDelete) {
                this.elements.bulkDelete.addEventListener('click', () => this.customerGroup_handleBulkDelete());
            }

            // Delete modal
            if (this.elements.confirmDelete) {
                this.elements.confirmDelete.addEventListener('click', () => this.customerGroup_confirmDelete());
            }
            if (this.elements.cancelDelete) {
                this.elements.cancelDelete.addEventListener('click', () => this.customerGroup_hideModal());
            }

            // Sort headers
            this.elements.sortHeaders.forEach(header => {
                header.addEventListener('click', (e) => this.customerGroup_handleSort(e));
            });

            // Individual delete buttons
            this.elements.deleteButtons.forEach(button => {
                button.addEventListener('click', (e) => this.customerGroup_handleSingleDelete(e));
            });

            // Modal backdrop and escape key
            if (this.elements.deleteModal) {
                this.elements.deleteModal.addEventListener('click', (e) => {
                    if (e.target === this.elements.deleteModal) {
                        this.customerGroup_hideModal();
                    }
                });
            }

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && !this.elements.deleteModal?.classList.contains('hidden')) {
                    this.customerGroup_hideModal();
                }
            });
        },

        customerGroup_initializeState() {
            this.customerGroup_updateBulkActionsVisibility();
        },

        customerGroup_handleSelectAll(event) {
            const isChecked = event.target.checked;
            
            this.elements.groupCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const groupId = checkbox.value;
                
                if (isChecked) {
                    this.state.selectedGroups.add(groupId);
                } else {
                    this.state.selectedGroups.delete(groupId);
                }
            });

            // Sync both select all checkboxes
            if (this.elements.selectAll && event.target !== this.elements.selectAll) {
                this.elements.selectAll.checked = isChecked;
            }
            if (this.elements.selectAllHeader && event.target !== this.elements.selectAllHeader) {
                this.elements.selectAllHeader.checked = isChecked;
            }

            this.customerGroup_updateBulkActionsVisibility();
        },

        customerGroup_handleCheckboxChange(event) {
            const groupId = event.target.value;
            const isChecked = event.target.checked;

            if (isChecked) {
                this.state.selectedGroups.add(groupId);
            } else {
                this.state.selectedGroups.delete(groupId);
            }

            // Update select all checkboxes
            const allChecked = this.elements.groupCheckboxes.length > 0 && 
                             Array.from(this.elements.groupCheckboxes).every(cb => cb.checked);
            
            if (this.elements.selectAll) {
                this.elements.selectAll.checked = allChecked;
            }
            if (this.elements.selectAllHeader) {
                this.elements.selectAllHeader.checked = allChecked;
            }

            this.customerGroup_updateBulkActionsVisibility();
        },

        customerGroup_updateBulkActionsVisibility() {
            if (this.elements.bulkActions) {
                if (this.state.selectedGroups.size > 0) {
                    this.elements.bulkActions.classList.remove('hidden');
                } else {
                    this.elements.bulkActions.classList.add('hidden');
                }
            }
        },

        customerGroup_handleBulkDelete() {
            if (this.state.selectedGroups.size === 0) {
                this.customerGroup_showNotification('Моля изберете групи за изтриване', 'warning');
                return;
            }

            const count = this.state.selectedGroups.size;
            this.elements.deleteMessage.textContent = 
                `Сигурни ли сте, че искате да изтриете ${count} избрани групи? Това действие не може да бъде отменено.`;
            
            this.customerGroup_showModal();
        },

        customerGroup_handleSingleDelete(event) {
            const groupId = event.target.closest('[data-customer-group-id]').dataset.customerGroupId;
            this.state.selectedGroups.clear();
            this.state.selectedGroups.add(groupId);
            
            this.elements.deleteMessage.textContent = 
                'Сигурни ли сте, че искате да изтриете тази група? Това действие не може да бъде отменено.';
            
            this.customerGroup_showModal();
        },

        customerGroup_confirmDelete() {
            if (this.state.selectedGroups.size === 0) return;

            this.customerGroup_hideModal();
            this.customerGroup_performDelete(Array.from(this.state.selectedGroups));
        },

        customerGroup_performDelete(groupIds) {
            const formData = new FormData();
            groupIds.forEach(id => {
                formData.append('selected[]', id);
            });
            formData.append('user_token', this.config.userToken);

            fetch(this.config.deleteUrl, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => this.customerGroup_handleDeleteResponse(data))
            .catch(error => this.customerGroup_handleDeleteError(error));
        },

        customerGroup_handleDeleteResponse(response) {
            if (response.success) {
                this.customerGroup_showNotification(response.success, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else if (response.error) {
                this.customerGroup_showNotification(response.error, 'error');
            }
        },

        customerGroup_handleDeleteError(error) {
            this.customerGroup_logDebug('Delete error:', error);
            this.customerGroup_showNotification('Възникна грешка при изтриването', 'error');
        },

        customerGroup_handleSort(event) {
            const sortField = event.currentTarget.dataset.sort;
            const url = new URL(window.location);
            
            let newOrder = 'ASC';
            if (url.searchParams.get('sort') === sortField && url.searchParams.get('order') === 'ASC') {
                newOrder = 'DESC';
            }
            
            url.searchParams.set('sort', sortField);
            url.searchParams.set('order', newOrder);
            url.searchParams.delete('page');
            
            window.location.href = url.toString();
        },

        customerGroup_showModal() {
            if (this.elements.deleteModal) {
                this.elements.deleteModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        },

        customerGroup_hideModal() {
            if (this.elements.deleteModal) {
                this.elements.deleteModal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        },

        customerGroup_showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        customerGroup_logDebug(...args) {
            if (window.console && window.console.log) {
                console.log('[CustomerGroupModule]', ...args);
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => CustomerGroupModule.init());
    } else {
        CustomerGroupModule.init();
    }

    // Expose module globally
    window.CustomerGroupModule = CustomerGroupModule;

})();
