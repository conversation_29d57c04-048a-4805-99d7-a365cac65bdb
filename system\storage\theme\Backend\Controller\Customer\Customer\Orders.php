<?php
namespace Theme25\Backend\Controller\Customer\Customer;

use Theme25\Backend\Controller\ControllerSubMethods;

class Orders extends ControllerSubMethods {
    
    public function __construct($controller) {
        parent::__construct($controller);
    }

    /**
     * Показва поръчките на клиента - връща HTML за tab view
     */
    public function execute() {
        try {
            $customer_id = (int)$this->requestGet('customer_id', 0);
            
            if (!$customer_id) {
                echo '<p class="text-gray-500">Невалиден клиент!</p>';
                return;
            }

            if (!$this->hasPermission('modify', 'customer/customer')) {
                echo '<p class="text-red-500">Нямате права за преглед на поръчки!</p>';
                return;
            }

            $this->loadModelAs('customer/customer', 'customerModel');
            $customer_info = $this->customerModel->getCustomer($customer_id);
            
            if (!$customer_info) {
                echo '<p class="text-red-500">Клиентът не съществува!</p>';
                return;
            }

            // Получаваме поръчките
            $orders_data = $this->getOrdersData($customer_id);
            
            // Генерираме HTML
            echo $this->generateOrdersListHtml($orders_data);
            
        } catch (Exception $e) {
            echo '<p class="text-red-500">Грешка при зареждане на поръчки: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
    }

    /**
     * AJAX метод за зареждане на още поръчки
     */
    public function loadmore() {
        $json = [];
        
        try {
            if (!$this->hasPermission('modify', 'customer/customer')) {
                $json['error'] = 'Нямате права за преглед на поръчки!';
            } else {
                $customer_id = (int)$this->requestGet('customer_id', 0);
                $start = (int)$this->requestGet('start', 0);
                
                if (!$customer_id) {
                    $json['error'] = 'Невалиден клиент!';
                } else {
                    $orders_data = $this->getOrdersData($customer_id, $start);
                    
                    if (!empty($orders_data['orders'])) {
                        $json['html'] = $this->generateOrdersItemsHtml($orders_data['orders']);
                        $json['has_more'] = ($start + 20) < $orders_data['total'];
                        $json['next_start'] = $start + 20;
                    } else {
                        $json['html'] = '';
                        $json['has_more'] = false;
                    }
                    
                    $json['success'] = true;
                }
            }
        } catch (Exception $e) {
            $json['error'] = 'Грешка при зареждане на поръчки: ' . $e->getMessage();
        }
        
        $this->setJSONResponseOutput($json);
    }

    /**
     * Генерира HTML за списъка с поръчки
     */
    private function generateOrdersListHtml($orders_data) {
        $html = '<div class="space-y-4">';
        
        // Header с общ брой поръчки
        $html .= '<div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i class="ri-shopping-bag-line text-blue-600"></i>
                    <span class="font-medium text-blue-900">Общо поръчки: ' . (int)$orders_data['total'] . '</span>
                </div>
                <div class="text-sm text-blue-700">
                    Показани: ' . count($orders_data['orders']) . ' от ' . (int)$orders_data['total'] . '
                </div>
            </div>
        </div>';
        
        if (empty($orders_data['orders'])) {
            $html .= '<p class="text-gray-500 text-center py-8">Няма поръчки за този клиент.</p>';
        } else {
            $html .= '<div id="orders-list" class="space-y-3">';
            $html .= $this->generateOrdersItemsHtml($orders_data['orders']);
            $html .= '</div>';
            
            // Load more button
            if ($orders_data['total'] > 20) {
                $html .= '<div class="text-center mt-6">
                    <button type="button" id="load-more-orders" 
                            class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors flex items-center mx-auto"
                            data-start="20" data-customer-id="' . (int)$orders_data['customer_id'] . '">
                        <i class="ri-add-line mr-2"></i>
                        Зареди още поръчки
                    </button>
                </div>';
            }
        }
        
        $html .= '</div>';
        
        return $html;
    }

    /**
     * Генерира HTML за отделните поръчки
     */
    private function generateOrdersItemsHtml($orders) {
        $html = '';
        
        foreach ($orders as $order) {
            $statusClass = $this->getOrderStatusClass($order['order_status']);
            $orderLink = $this->getAdminLink('sale/order/info', 'order_id=' . (int)$order['order_id']);
            
            $html .= '
                <div class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:border-primary/50 transition-colors">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <i class="ri-file-list-3-line text-gray-600"></i>
                        </div>
                        <div>
                            <div class="flex items-center space-x-2">
                                <a href="' . htmlspecialchars($orderLink) . '" 
                                   class="font-medium text-primary hover:text-primary/80 transition-colors" 
                                   target="_blank">
                                    Поръчка #' . (int)$order['order_id'] . '
                                </a>
                                <i class="ri-external-link-line text-xs text-gray-400"></i>
                            </div>
                            <div class="text-sm text-gray-500">
                                ' . htmlspecialchars($order['date_added']) . '
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <div class="font-medium text-gray-900">' . 
                            htmlspecialchars($order['total_formatted']) . '</div>
                            <div class="text-xs text-gray-500">' . 
                            htmlspecialchars($order['currency_code']) . '</div>
                        </div>
                        <div>
                            <span class="px-3 py-1 text-xs font-medium rounded-full ' . $statusClass . '">' . 
                            htmlspecialchars($order['order_status']) . '</span>
                        </div>
                    </div>
                </div>';
        }
        
        return $html;
    }

    /**
     * Получава данните за поръчките
     */
    private function getOrdersData($customer_id, $start = 0) {
        $limit = 20;
        
        $this->loadModelAs('sale/order', 'orderModel');
        
        $filter_data = [
            'filter_customer_id' => $customer_id,
            'start' => $start,
            'limit' => $limit,
            'sort' => 'o.date_added',
            'order' => 'DESC'
        ];
        
        $orders = $this->orderModel->getOrders($filter_data);
        $total = $this->orderModel->getTotalOrders(['filter_customer_id' => $customer_id]);
        
        $orders_data = [];
        foreach ($orders as $order) {
            $orders_data[] = [
                'order_id' => $order['order_id'],
                'order_status' => $order['status'],
                'total' => (float)$order['total'],
                'total_formatted' => $this->currency->format($order['total'], $order['currency_code'], $order['currency_value']),
                'currency_code' => $order['currency_code'],
                'date_added' => date('d.m.Y H:i', strtotime($order['date_added']))
            ];
        }
        
        return [
            'orders' => $orders_data,
            'total' => $total,
            'customer_id' => $customer_id
        ];
    }

    /**
     * Връща CSS класове за статуса на поръчката
     */
    private function getOrderStatusClass($status) {
        $status_lower = strtolower($status);
        
        if (strpos($status_lower, 'complete') !== false || strpos($status_lower, 'завършен') !== false) {
            return 'bg-green-100 text-green-800';
        } elseif (strpos($status_lower, 'pending') !== false || strpos($status_lower, 'чакащ') !== false) {
            return 'bg-yellow-100 text-yellow-800';
        } elseif (strpos($status_lower, 'processing') !== false || strpos($status_lower, 'обработва') !== false) {
            return 'bg-blue-100 text-blue-800';
        } elseif (strpos($status_lower, 'shipped') !== false || strpos($status_lower, 'изпратен') !== false) {
            return 'bg-purple-100 text-purple-800';
        } elseif (strpos($status_lower, 'canceled') !== false || strpos($status_lower, 'отказан') !== false) {
            return 'bg-red-100 text-red-800';
        } else {
            return 'bg-gray-100 text-gray-800';
        }
    }
}
